//
//  ;OS.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2022/7/8.
//

#import "FlutteriOS.h"
#import <AVFoundation/AVFoundation.h>

@interface FlutteriOS ()

@property (nonatomic, strong) AVAudioPlayer *player;

@end

@implementation FlutteriOS

+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar> *)registrar{
    FlutterMethodChannel *channel = [FlutterMethodChannel methodChannelWithName:@"org/metatel/flutter_plugin" binaryMessenger:[registrar messenger]];
    FlutteriOS *instance = [[FlutteriOS alloc] init];
    [registrar addMethodCallDelegate:instance channel:channel];
}

- (void)handleMethodCall:(FlutterMethodCall *)call result:(FlutterResult)result {
    if ([call.method isEqualToString:@"Speakerphone"]) {
        //接受fultter 传过来的参数
        NSLog(@"---%@",call.arguments);
        BOOL res = [call.arguments[@"enable"] boolValue];
//        BOOL isBack = [call.arguments[@"isBack"] boolValue];
        NSLog(@"Speakerphone === %d",res);
        [self speakerPhone:res isBack:NO];
        
       result(@YES);
    } else if ([call.method isEqualToString:@"requestAudioFocus"]) {
                   //接受fultter 传过来的参数
                   NSLog(@"---%@",call.arguments);
                   //BOOL res = [call.arguments[@"isRequest"] boolValue];
                   NSLog(@"requestAudioFocus ===== %d",0);
        NSError *error = nil;
        AVAudioSession *session = [AVAudioSession sharedInstance]; //获取共享单例

//也可以这样释放音频焦点, 为了确保之前被打断的正在播放音频的App能收到resume消息。
            if ([session setActive:NO withOptions:AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation error:&error] == NO )
            {
                NSLog(@"%@",error); //发生错误
            result(@NO);
            }
                     result(@YES);
               }else if([call.method isEqualToString:@"startRuning"]){
        [self startRuning];
    } else if([call.method isEqualToString:@"stopRuning"]){
        [self stopRuning];
    }else{
        result(FlutterMethodNotImplemented);
    }
}

- (void)speakerPhone:(BOOL)res isBack:(BOOL)isBack{
    NSError *error = nil;
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    
//    [audioSession setCategory:AVAudioSessionCategoryPlayAndRecord
//                  withOptions:res ? AVAudioSessionCategoryOptionDefaultToSpeaker
//                             :
//     AVAudioSessionCategoryOptionAllowBluetooth|AVAudioSessionCategoryOptionAllowBluetoothA2DP
//                        error:nil];
//    [audioSession setActive:YES error:&error];
//    NSLog(@"1 error -- %@",error);
//    [audioSession setActive:YES error:&error];
    
    
    if (res) {
        NSLog(@"res === 1");
        [audioSession setCategory:AVAudioSessionCategoryPlayAndRecord withOptions:AVAudioSessionCategoryOptionDefaultToSpeaker error:&error];
        [audioSession overrideOutputAudioPort:AVAudioSessionPortOverrideSpeaker error:&error];
        NSLog(@"1 error -- %@",error);
    }else{
        NSLog(@"res === 0");
        [audioSession setCategory:AVAudioSessionCategoryPlayAndRecord withOptions:AVAudioSessionCategoryOptionAllowBluetooth|AVAudioSessionCategoryOptionAllowBluetoothA2DP error:&error];
        [audioSession overrideOutputAudioPort:AVAudioSessionPortOverrideNone error:&error];

        NSLog(@"2 error -- %@",error);
    }
    [audioSession setActive:YES error:nil];
//
    NSLog(@"2 error -- %@",error);
}

- (void)startRuning{
    NSLog(@"---->sh start running.  third == %@",[NSThread currentThread]);
    
    
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    BOOL back = YES;
    [audioSession setCategory:AVAudioSessionCategoryPlayback withOptions:AVAudioSessionCategoryOptionDuckOthers error:nil];
    // 如果短时间内修改过这个方法，记得重定向输出设备，明天注意看
    [audioSession overrideOutputAudioPort:AVAudioSessionPortOverrideSpeaker error:nil];
    [audioSession setActive:YES withOptions:AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation error:nil];
    
    NSString *audioFile = [[NSBundle mainBundle] pathForResource:@"Happytime.wav" ofType:nil];
    NSLog(@"audio path == %@",audioFile);
    NSError *error;
    self.player = [[AVAudioPlayer alloc]initWithContentsOfURL:[NSURL fileURLWithPath:audioFile] error:&error];
    if (error) {
        NSLog(@"play error == %@",error);
        return;
    }
//    player.delegate = self;
    self.player.numberOfLoops = -1;
    [self.player prepareToPlay];
    [self.player play];
    
    NSLog(@"---->sh 已经调用了播放");
}

- (void)stopRuning{
    NSLog(@"---->sh stop runing");
    if (self.player) {
        [self.player pause];
        self.player = nil;
    }
}


@end
