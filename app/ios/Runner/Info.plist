<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>ShareHandler</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.image</string>
				<string>public.movie</string>
				<string>public.file-url</string>
				<string>public.url</string>
				<string>public.text</string>
			</array>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(APP_BUILD_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ShareMedia-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				<string>$(PRODUCT_BUNDLE_IDENTIFIER).open</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<string>No</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>Convenient for you to share within the app</string>
	<key>NFCReaderUsageDescription</key>
    <string>We need access to NFC for reading tags</string>
	<key>NSCameraUsageDescription</key>
	<string>It is convenient for you to shoot and make audio and video calls within the app</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Why is my app authenticating using face id?</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Convenient for recording sound when using voice messages, audio and video calls, and shooting videos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Convenient for you to share photos and videos within the app</string>
	<key>NSUserActivityTypes</key>
	<array>
		<string>INSendMessageIntent</string>
	</array>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>voip</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarStyle</key>
	<string></string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportsDocumentBrowser</key>
	<true/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>LSMinimumSystemVersion</key>
    <string>12.0.0</string>
    <key>com.apple.developer.nfc.readersession.felica.systemcodes</key>
    	<array>
    		<string>8008</string>
    	</array>
    <key>com.apple.developer.nfc.readersession.formats</key>
              <array>
                  <string>NDEF</string>
                  <string>TAG</string>
              </array>
    <key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
    	<array>
    		<string>00A4040007D2760085010000</string>
    	</array>
</dict>
</plist>
