import UIKit
import Flutter

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        //self.window.makeSecure() //Add this line
        GeneratedPluginRegistrant.register(with: self)
        FlutteriOS.register(with: self.registrar(forPlugin: "FlutteriOS")!)
        let noticCenter = UNUserNotificationCenter.current()
        noticCenter.requestAuthorization(options: [.badge, .sound, .alert]) { granted, error in
            if granted {
                print("notic-通知开启")
            } else {
                print("notic-关闭通知")
            }
        }
        noticCenter.getNotificationSettings(completionHandler: { settings in
            print("notic-settings-\(settings)")
        })
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
}
//And this extension
extension UIWindow {
func makeSecure() {
    let field = UITextField()
    field.isSecureTextEntry = true
    self.addSubview(field)
    field.centerYAnchor.constraint(equalTo: self.centerYAnchor).isActive = true
    field.centerXAnchor.constraint(equalTo: self.centerXAnchor).isActive = true
    self.layer.superlayer?.addSublayer(field.layer)
    field.layer.sublayers?.first?.addSublayer(self.layer)
  }
}
