//
//  AVAudioSession+ReSet.m
//  AudioTest
//
//  Created by <PERSON> on 2022/7/12.
//

#import "AVAudioSession+ReSet.h"
#import <objc/runtime.h>

@implementation AVAudioSession (ReSet)


+ (void)load{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Method oldMethod = class_getInstanceMethod(self, @selector(setCategory:error:));
        Method newMethod = class_getInstanceMethod(self, @selector(MySetCategory:error:));
        method_exchangeImplementations(oldMethod, newMethod);
        
        oldMethod = class_getInstanceMethod(self, @selector(setCategory:withOptions:error:));
        newMethod = class_getInstanceMethod(self, @selector(mySetCategory:withOptions:error:));
        method_exchangeImplementations(oldMethod, newMethod);
        
        oldMethod = class_getInstanceMethod(self, @selector(setCategory:mode:options:error:));
        newMethod = class_getInstanceMethod(self, @selector(mySetCategory:mode:options:error:));
        method_exchangeImplementations(oldMethod, newMethod);
        
        oldMethod = class_getInstanceMethod(self, @selector(setCategory:mode:routeSharingPolicy:options:error:));
        newMethod = class_getInstanceMethod(self, @selector(mySetCategory:mode:routeSharingPolicy:options:error:));
        method_exchangeImplementations(oldMethod, newMethod);
    });
}

- (BOOL)mySetCategory:(AVAudioSessionCategory)category error:(NSError *__autoreleasing  _Nullable *)outError{
    NSLog(@"---->sh mySetCategory category error");
    return [self mySetCategory:category error:outError];
}

- (BOOL)mySetCategory:(AVAudioSessionCategory)category withOptions:(AVAudioSessionCategoryOptions)options error:(NSError *__autoreleasing  _Nullable *)outError{
    NSLog(@"---->sh mySetCategory category options error");
    return [self mySetCategory:category withOptions:options error:outError];
}

- (BOOL)mySetCategory:(AVAudioSessionCategory)category mode:(AVAudioSessionMode)mode options:(AVAudioSessionCategoryOptions)options error:(NSError *__autoreleasing  _Nullable *)outError{
    NSLog(@"---->sh mySetCategory category model options error");
    return  [self mySetCategory:category mode:mode options:options error:outError];
}

- (BOOL)mySetCategory:(AVAudioSessionCategory)category mode:(AVAudioSessionMode)mode routeSharingPolicy:(AVAudioSessionRouteSharingPolicy)policy options:(AVAudioSessionCategoryOptions)options error:(NSError *__autoreleasing  _Nullable *)outError{
    NSLog(@"---->sh mySetCategory category model policy options error");
    return [self mySetCategory:category mode:mode routeSharingPolicy:policy options:options error:outError];
}

@end
