allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://mirrors.tuna.tsinghua.edu.cn/flutter/download.flutter.io' }
//        maven {url 'https://developer.huawei.com/repo/'}
//        maven { url 'https://developer.hihonor.com/repo/' }
        maven { url 'https://gitee.com/ggv-le/pushLib/raw/main/'}
//        maven { url 'E:\\WorkSpaceFlutter\\push_libs_maven'}
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.application") ||
                project.plugins.hasPlugin("com.android.library")) {
            project.android {
                compileSdkVersion 34
                buildToolsVersion "34.0.0"
            }
        }
    }
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
