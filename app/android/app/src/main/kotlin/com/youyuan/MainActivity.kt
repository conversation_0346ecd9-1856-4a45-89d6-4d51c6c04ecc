package com.youyuan

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.os.Bundle
import android.provider.Settings
import android.util.AttributeSet
import android.view.View
import android.view.WindowManager
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import java.io.File
import android.net.Uri
import android.util.Log
import com.youyuan.Utils

import io.dcloud.feature.sdk.*;
import io.dcloud.feature.sdk.Interface.IUniMP
import io.dcloud.feature.sdk.DCSDKInitConfig
import io.dcloud.common.adapter.util.Logger

import io.dcloud.feature.sdk.MenuActionSheetItem
import io.dcloud.feature.unimp.DCUniMPJSCallback
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration
import io.dcloud.feature.unimp.config.UniMPReleaseConfiguration


import com.alibaba.fastjson.*
import io.flutter.plugin.common.EventChannel.EventSink
import io.flutter.plugin.common.EventChannel


class MainActivity: FlutterFragmentActivity()   {

    var eventSink: EventSink? = null

    // 小程序实例
    val unimpMap = mutableMapOf<String?, IUniMP?>();

    var uniMpcallback:DCUniMPJSCallback? = null
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "org/metatel/flutter_plugin")
        methodChannel.setMethodCallHandler { methodCall: MethodCall, result: MethodChannel.Result ->
            onMethodCall(methodCall,result)
        }

        // 小程序Event 对象

        val event = EventChannel(flutterEngine.dartExecutor.binaryMessenger, "flutter_uni_stream")
        event.setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(arguments: Any?, events: EventChannel.EventSink) {
                    eventSink = events
                    Log.d("Android", "EventChannel onListen called")
                }

                override fun onCancel(arguments: Any?) {
                    Log.w("Android", "EventChannel onCancel called")
                }
            })
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
    
    fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        android.util.Log.d("onMethodCall","call.method ="+call.method)
        when (call.method) {
//            "open" -> {
//                try {
//                    // 接收 Flutter 传入的参数
//                    val argument = call.argument<String>("AppID")
//                    // 设置右上角胶囊操作菜单
//                    val item = MenuActionSheetItem("关于", "about")
//                    val sheetItems: MutableList<MenuActionSheetItem> = ArrayList()
//                    sheetItems.add(item)
//                    // 初始化uniMPSDK
//                    val config = DCSDKInitConfig.Builder()
//                        .setCapsule(true)
//                        .setMenuDefFontSize("16px")
//                        .setMenuDefFontColor("#2D2D2D")
//                        .setMenuDefFontWeight("normal")
//                        .setMenuActionSheetItems(sheetItems)
//                        .build()
//                    DCUniMPSDK.getInstance().initialize(this, config)
//
//                    // 打开小程序
//                    DCUniMPSDK.getInstance().openUniMP(this, argument)
//                    // 监听胶囊菜单点击事件
//                    DCUniMPSDK.getInstance().setDefMenuButtonClickCallBack { argumentAppID, id ->
//                        when (id) {
//                            "about" -> {
//                                Logger.e(argumentAppID + "点击了关于")
//                            }
//                        }
//                    }
//                    // 监听小程序关闭
//                    DCUniMPSDK.getInstance()
//                        .setUniMPOnCloseCallBack { argumentAppID -> Log.e("unimp", argumentAppID + "被关闭了") }
//                } catch (e: Exception) {
//                    e.printStackTrace()
//                }
//            }
//            "brightness" -> {
//                result.success(getBrightness())
//            }
            "setBrightness" -> {
                val brightness = call.argument<Double>("brightness")!!
                val layoutParams: WindowManager.LayoutParams = window.attributes
                layoutParams.screenBrightness = brightness.toFloat()
                window.attributes = layoutParams
                result.success(null)
            }
            "isKeptOn" -> {
                val flags: Int = window.attributes.flags
                result.success(flags and WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON != 0)
            }
            "keepOn" -> {
                val on = call.argument<Boolean>("on")
                if (on!!) {
                    println("Keeping screen on ")
                    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                } else {
                    println("Not keeping screen on")
                    window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                }
                result.success(null)
            }
            "Speakerphone"->{
                val enable = call.argument<Boolean>("enable")!!
                val isCall = call.argument<Boolean>("isCall")!!
                val audioManager:AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
                println(
                    "Speakerphone 1111 start isCall:$isCall audioManager.mode:${audioManager.mode} speaker:${audioManager.isSpeakerphoneOn} enable:" +
                            "$enable"
                )
                if (enable) {
                    if (isCall){
                        audioManager.mode =AudioManager.MODE_IN_COMMUNICATION
                    }else{
                    audioManager.mode = AudioManager.MODE_NORMAL
                    }
                } else {
                    audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
                }
                audioManager.isSpeakerphoneOn = enable
                println("Speakerphone 1111 end isCall:$isCall audioManager.mode:${audioManager.mode} speaker:${audioManager.isSpeakerphoneOn}")
                result.success(null)
            }
            "setmode"->{
                val value = call.argument<Int>("value")!!
                println("setmode start value:$value")
                val audioManager:AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
                audioManager.mode=value
                println("setmode end audioManager.mode:${audioManager.mode}")
                result.success(null)
            }
            "isSpeakerphoneOn"->{
                val audioManager:AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
                val  isSpeak=audioManager.isSpeakerphoneOn
                result.success(isSpeak)

            }
            "runOnEmulator"->{
//                val runOnEmulator = AndroidDeviceIMEIUtil.isRunOnEmulator(this) //特征值判断
//                val runOnEmulatorAll = EmulatorDetectUtil.isEmulatorFromAll(this) //激进判断
//                Log.d("runOnEmulator","runOnEmulator: $runOnEmulator");
//                Log.d("runOnEmulator","runOnEmulatorAll: $runOnEmulatorAll");
                result.success(false)

            }
            "toSystemHome"->{
                val intent = Intent() // 创建Intent对象
                intent.action = Intent.ACTION_MAIN // 设置Intent动作
                intent.addCategory(Intent.CATEGORY_HOME) // 设置Intent种类
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK //标记
                startActivity(intent)
                result.success(null)
            }
            "requestAudioFocus"->{
                val value = call.argument<Boolean>("isRequest")!!
                requestAudioFocus(value)
                result.success(null)
            }
            "restartApp"->{
                android.os.Process.killProcess(android.os.Process.myPid())
            }
            "getAndroidId"->{
                result.success(getAndroidId())
            }
            "updateFile"->{
                val path = call.argument<String>("path")!!
                broadcastFileUpdate(path)
                result.success(null)
            }
            "isRoot"->{
                result.success(isRoot())
            }
            "isOpenedAdb"->{
                result.success(isOpenedAdb())
            }
            "isOpenedMore"->{
                result.success(isOpenedMore())
            }
            "disableLockScreen"->{
                val on = call.argument<Boolean>("on")
                disableLockScreen(on!!)
                result.success(null)
            }
            "initMP" -> {
                try {
                    if (DCUniMPSDK.getInstance().isInitialize()) {
                        result.success(2)
                    } else {
                        // 设置右上角胶囊操作菜单
//                        val item = MenuActionSheetItem("关于", "gy")
//                        val sheetItems: MutableList<MenuActionSheetItem> = ArrayList()
//                        sheetItems.add(item)
                        // 初始化uniMPSDK
                        val config = DCSDKInitConfig.Builder()
                            .setCapsule(false)
                            .setMenuDefFontSize("16px")
                            .setMenuDefFontColor("#2D2D2D")
                            .setMenuDefFontWeight("normal")
                            .setEnableBackground(true)
//                            .setMenuActionSheetItems(sheetItems)
                            .build()
                        DCUniMPSDK.getInstance().initialize(this, config)

                        //监听胶囊点击事件
                        DCUniMPSDK.getInstance()
                            .setCapsuleMenuButtonClickCallBack { argumentAppID ->
                                val backdata = JSONObject().apply {
                                    set("appid", argumentAppID)
                                    set("event", "capsuleaction")
                                }
                                println("MainActivity backdata:$backdata")
                                eventSink?.success(backdata)
                            }

                        // 监听小程序关闭
                        DCUniMPSDK.getInstance().setUniMPOnCloseCallBack { argumentAppID ->
                            if (unimpMap.containsKey(argumentAppID)) {
                                unimpMap.remove(argumentAppID)
                                unimpMap[argumentAppID]?.closeUniMP();
                            }
                            val backdata = JSONObject().apply {
                                set("appid", argumentAppID)
                                set("event", "close")
                            }
                            println("MainActivity backdata:$backdata")
                            eventSink?.success(backdata)
                        }

                        //监听小程序向原生发送事件回调方法
                        DCUniMPSDK.getInstance()
                            .setOnUniMPEventCallBack { argumentAppID, event, data, callback ->
                                val backdata = JSONObject().apply {
                                    set("appid", argumentAppID)
                                    set("event", event)
                                    set("data", data)
                                }
                                println("MainActivity backdata:$backdata")

                                eventSink?.success(backdata)
                                uniMpcallback = callback
                            }

                        result.success(1)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }

            /** 检查指定的 UniMP 小程序
             * {
             *      "appid": ""
             * }
             */
            "checkMP" -> {
                try {
                    // 接收 Flutter 传入的参数
                    val argumentAppID: String? = call.argument<String>("appid")
                    if (DCUniMPSDK.getInstance().isExistsApp(argumentAppID)) {
                        result.success(true)
                    } else {
                        result.success(false)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }

            /** 获取指定的 UniMP 小程序版本
             * {
             *      "appid": ""
             * }
             */
            "versionMP" -> {
                try {
                    // 接收 Flutter 传入的参数
                    val argumentAppID: String? = call.argument<String>("appid")
                    val res = DCUniMPSDK.getInstance().getAppVersionInfo(argumentAppID)
                    var versionStr: String? = null;
                    if(res != null) {
                        versionStr = res.toString()
                    }
                    println("MainActivity versionStr:$versionStr")

                    result.success(versionStr)
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }

            /** 安装 UniMP 小程序
             * {
             *      "appid": ""，
             *      "wgtPath": ""
             * }
             */
            "installMP" -> {
                try {
                    // 接收 Flutter 传入的参数
                    val argumentAppID: String? = call.argument<String>("appid")
                    val wgtPath: String? = call.argument<String>("wgtPath")
                    val releaseConfig = UniMPReleaseConfiguration()
                    releaseConfig.wgtPath = wgtPath
                    DCUniMPSDK.getInstance().releaseWgtToRunPath(
                        argumentAppID,
                        releaseConfig
                    ) { code, res ->
                        println("MainActivity res:$res code=$code" )

                        if (code == 1) {
                            result.success(true)
                        } else {
                            result.success(false)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }

            /** 打开指定的 UniMP 小程序
             * {
             *      "appid": "",
             *      "isreload": true //重新打开
             *      "config": {
             *          "extraData": {},  //其他自定义参数JSON
             *          "path": "" //指定启动应用后直接打开的页面路径
             *      }
             * }
             */
            "openMP" -> {
                try {
                    // 接收 Flutter 传入的参数
                    val argumentAppID: String? = call.argument<String>("appid")
                    if (unimpMap.containsKey(argumentAppID) == false) {
                        val argumentConfig: HashMap<String,Any>? = call.argument<HashMap<String,Any>>("config")
                        val uniMPOpenConfiguration = UniMPOpenConfiguration()
                        if (argumentConfig != null && argumentConfig.containsKey("extraData")) {
                            val jsonObject = org.json.JSONObject()
                            var extraData = argumentConfig.get("extraData") as HashMap<String,Any>
                            extraData.forEach { s, any ->  jsonObject.put(s,any) }
                            jsonObject.put("path",argumentConfig.get("path") as String?)
                            uniMPOpenConfiguration.extraData = jsonObject
                        }
                        if (argumentConfig != null && argumentConfig.containsKey("path")) {
                            uniMPOpenConfiguration.path = argumentConfig.get("path") as String?
                        }
                        if (argumentConfig != null && argumentConfig.containsKey("arguments")) {
                            val jsonObject = org.json.JSONObject()
                            var arguments = argumentConfig.get("arguments") as HashMap<String,Any>
                            arguments.forEach { s, any ->  jsonObject.put(s,any) }
                            uniMPOpenConfiguration.arguments = jsonObject
                        }
                        // 打开小程序
                        unimpMap[argumentAppID] = DCUniMPSDK.getInstance()
                            .openUniMP(applicationContext, argumentAppID, uniMPOpenConfiguration)
                        result.success(true)
                    } else {
                        val data = call.argument<Any>("config")
                        val backdata = JSONObject().apply {
                            set("appid", argumentAppID)
                            set("data", data)
                        }
                        unimpMap[argumentAppID]?.sendUniMPEvent("open_app", backdata)
                        unimpMap[argumentAppID]?.showUniMP();
                        result.success(true)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }

            /** 隐藏指定的 UniMP 小程序
             * {
             *      "appid": "",
             * }
             */
            "hideMP" -> {
                try {
                    // 接收 Flutter 传入的参数
                    val argumentAppID: String? = call.argument<String>("appid")
                    if (unimpMap.containsKey(argumentAppID)) {
                        unimpMap[argumentAppID]?.hideUniMP();
                    }
                    result.success(true)
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }

            /** 关闭指定的 UniMP 小程序
             * {
             *      "appid": "",
             * }
             */
            "closeMP" -> {
                try {
                    // 接收 Flutter 传入的参数
                    val argumentAppID: String? = call.argument<String>("appid")
                    if (unimpMap.containsKey(argumentAppID)) {
                        unimpMap.remove(argumentAppID)
                        unimpMap[argumentAppID]?.closeUniMP();
                    }
                    result.success(true)
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }

            /**发送数据到指定的UniMP小程序
             * {
             *      "appid": "",
             *      "event": "",
             *      "data": {}
             * }
             */
            "sendMP" -> {
                try {
                    // 接收 Flutter 传入的参数
                    val argumentAppID = call.argument<String>("appid")
                    val sendEvent = call.argument<String>("event")
                    val data = call.argument<Any>("data")

                    val backdata = JSONObject().apply {
                        set("appid", argumentAppID)
                        set("event", sendEvent)
                        set("data", data)
                    }
                    unimpMap[argumentAppID]?.sendUniMPEvent(sendEvent, backdata)
                    result.success(true)
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }

            /** 回调数据到到指定的UniMP小程序
             * {
             *      "appid": "",
             *      "event": "",
             *      "data": {}
             * }
             */
            "callbackMP" -> {
                try {
                    // 接收 Flutter 传入的参数
                    val argumentAppID = call.argument<String>("appid")
                    val sendEvent = call.argument<String>("event")
                    val data = call.argument<Any>("data")

                    val backdata = JSONObject().apply {
                        set("appid", argumentAppID)
                        set("event", sendEvent)
                        set("data", data)
                    }
                    println("MainActivity callbackMP: backdata=$backdata" )
                    uniMpcallback?.invoke(backdata)
                    result.success(true)
                } catch (e: Exception) {
                    e.printStackTrace()
                    errorMsg("error_code",e.printStackTrace().toString())
                    result.error("error_code", e.message, e.printStackTrace().toString())
                }
            }
            else -> result.notImplemented()
        }
    }
    private fun errorMsg(tag: String,data:String) {
        Log.e(tag,data)
    }

    private fun disableLockScreen(request: Boolean) {
        if (request == true) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
        } else {
            window.clearFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
        }
    }

    private fun broadcastFileUpdate(path: String) {
        sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(File(path))))
        println("updated!"+path)
    }

    private fun getBrightness(): Float {
        var result: Float = window.attributes.screenBrightness
        if (result < 0) { // the application is using the system brightness
            try {
                result = Settings.System.getInt(contentResolver, Settings.System.SCREEN_BRIGHTNESS) / 255.toFloat()
            } catch (e: Settings.SettingNotFoundException) {
                result = 1.0f
                e.printStackTrace()
            }
        }
        return result
    }
    @SuppressLint("HardwareIds")
    private fun getAndroidId(): String? {
        return Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
    }
    /**获取焦点 */
    private var audioFocused = false
    fun requestAudioFocus(request: Boolean) {
        val audioManager:AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
        if (request) {
            if (!audioFocused) {
                val res = audioManager.requestAudioFocus(null, AudioManager.STREAM_VOICE_CALL, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                //Logger.d("Audio focus requested: " + (res == AudioManager.AUDIOFOCUS_REQUEST_GRANTED ? "Granted" : "Denied"));
                if (res == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                    audioFocused = true
                }
            }
        } else {
            if (audioFocused) {
                audioManager.abandonAudioFocus(null)
                audioFocused = false
            }
        }
    }
    fun isOpenedAdb():Boolean {
        val enableAdb: Boolean =
            Settings.Secure.getInt(getContentResolver(), Settings.Secure.ADB_ENABLED, 0) > 0
        return  enableAdb;
    }
    fun isRoot(): Boolean {
        var res = false
        try {
            res = !(!File("/system/bin/su").exists() &&
                    !File("/system/xbin/su").exists())
        } catch (e: Exception) {
        }
        return res
    }

    fun isOpenedMore():Boolean {
        val enableAdb: Boolean =
            Utils().haveDualAppEx(this)
        return  enableAdb;
    }
}
