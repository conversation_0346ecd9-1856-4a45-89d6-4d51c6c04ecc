<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.youyuan">
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.USE_BIOMETRIC"/>
    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
<!--    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"-->
<!--        tools:ignore="ScopedStorage" />-->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>

    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC"/>

    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" tools:node="remove"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" tools:node="remove"/>
<!--    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" tools:node="remove"/>-->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" tools:node="remove"/>


    <!--  支付宝-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />


    <application
        android:icon="@mipmap/ic_launcher"
        tools:replace="android:label"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:extractNativeLibs="true"
        android:usesCleartextTraffic="true">

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths"
                tools:replace="android:resource" />
        </provider>
        <receiver
            android:name="com.randomforest.time_change_detector.TimeChangeDetectorPlugin"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
                <action android:name="android.intent.action.DATE_CHANGED"/>
            </intent-filter>
        </receiver>

        <!-- 微信支付配置 start -->
        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="video/*" />
                <data android:mimeType="image/*" />
                <data android:mimeType="application/pdf" />
                <!--prf-->
                <data android:mimeType="application/pics-rules" />
                <!--pot pos ppt-->
                <data android:mimeType="application/vnd.ms-powerpoint" />
                <!--js-->
                <data android:mimeType="application/x-javascript" />
                <!--zip-->
                <data android:mimeType="application/zip" />
                <!--rar-->
                <data android:mimeType="application/rar" />
                <!--rar-->
                <data android:mimeType="application/x-rar-compressed" />
                <!--jar-->
                <data android:mimeType="application/ava-archive" />
                <!--tar-->
                <data android:mimeType="application/x-tar" />
                <!--tgz-->
                <data android:mimeType="application/x-compressed" />
                <!--zip-->
                <data android:mimeType="application/x-zip-compressed" />

                <!--ppt-->
                <data android:mimeType="application/vnd.ms-powerpoint" />
                <!--xls-->
                <data android:mimeType="application/vnd.ms-excel" />
                <!--doc、dot-->
                <data android:mimeType="application/msword" />
                <!--docx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
                <!--potx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.template" />
                <!--pptx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation" />
                <!--xlsx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
                <!--xltx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.template" />
                <!--ppsx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.slideshow" />
                <!--dotx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.template" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="video/*" />
                <data android:mimeType="image/*" />
                <data android:mimeType="text/plain" />

                <data android:mimeType="application/pdf" />
                <!--prf-->
                <data android:mimeType="application/pics-rules" />
                <!--pot pos ppt-->
                <data android:mimeType="application/vnd.ms-powerpoint" />
                <!--js-->
                <data android:mimeType="application/x-javascript" />
                <!--zip-->
                <data android:mimeType="application/zip" />
                <!--rar-->
                <data android:mimeType="application/rar" />
                <!--rar-->
                <data android:mimeType="application/x-rar-compressed" />
                <!--jar-->
                <data android:mimeType="application/ava-archive" />
                <!--tar-->
                <data android:mimeType="application/x-tar" />
                <!--tgz-->
                <data android:mimeType="application/x-compressed" />
                <!--zip-->
                <data android:mimeType="application/x-zip-compressed" />

                <!--ppt-->
                <data android:mimeType="application/vnd.ms-powerpoint" />
                <!--xls-->
                <data android:mimeType="application/vnd.ms-excel" />
                <!--doc、dot-->
                <data android:mimeType="application/msword" />
                <!--docx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
                <!--potx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.template" />
                <!--pptx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation" />
                <!--xlsx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
                <!--xltx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.template" />
                <!--ppsx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.slideshow" />
                <!--dotx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.template" />
            </intent-filter>
            <meta-data
                android:name="android.service.chooser.chooser_target_service"
                android:value="androidx.sharetarget.ChooserTargetServiceCompat" />
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/share_targets" />
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Accepts URIs that begin with YOUR_SCHEME://YOUR_HOST -->
                <data
                    android:scheme="${URL_SCHEME}"
                    android:host="${HOST}" />
            </intent-filter>

            <!-- App Links -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Accepts URIs that begin with https://YOUR_HOST -->
                <data
                    android:scheme="https"
                    android:host="${HOST}" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
       <service
           android:name="com.dexterous.flutterlocalnotifications.ForegroundService"
           android:exported="false"
           android:foregroundServiceType="dataSync"
           android:stopWithTask="false"/>
        <!--google fcm初始化-->
        <meta-data
            android:name="myicon"
            android:resource="@drawable/ic_launcher" />
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more. -->
        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="false" />
        <!-- A custom Android Notification Channel to deliver FCM notifications on a non-default channel -->
        <meta-data
            android:name="com.google.firebase.messaging.org.im.call.notification"
            android:value="org.im.call.notification" />
        <meta-data
            android:name="com.google.firebase.messaging.org.im.msg.notification"
            android:value="org.im.msg.notification" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="org.im.msg.notification" />
        <provider
            android:name="com.pichillilorenzo.flutter_inappwebview_android.InAppWebViewFileProvider"
            android:authorities="${applicationId}.flutter_inappwebview_android.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <!--上架华为市场需要移除 tools:node="remove"
           <action android:name="android.intent.action.MEDIA_BUTTON" />
        -->
        <receiver android:name="flutter.moum.headset_event.HeadsetBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.HEADSET_PLUG" />
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

        <!--<provider
            android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"
            android:authorities="${applicationId}.flutter_downloader.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>-->
    </application>
</manifest>
