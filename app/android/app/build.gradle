plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}


def isHwMetatel = true//海外metatel
def isGooglPlay = false

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    compileSdkVersion 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    splits {
        abi {
            enable true
            reset()
            include 'arm64-v8a'
            universalApk true
        }
    }
    defaultConfig {
        applicationId "com.youyuan"
        minSdkVersion 26
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        ndk {
            abiFilters /*'armeabi-v7a',*/'arm64-v8a'
        }
        manifestPlaceholders = [
                VIVO_APPKEY : "622c86374fcd3b6ba87080e1875b76d0",
                VIVO_APPID  : "105619714",
                //
                HONOUR_APPID: "11111111",

                URL_SCHEME: "metatel",
                HOST: applicationId+".open",
        ]

    }
    lintOptions {
        // 如打包出现Failed to transform libs.jar to match attributes
        checkReleaseBuilds false
    }

    sourceSets {
        main {
            if (isGooglPlay) {
                manifest.srcFile 'src/main/googleplay/AndroidManifest.xml'
            } else {
                manifest.srcFile 'src/main/AndroidManifest.xml'
            }
            res.srcDirs = ['src/main/hw_res']


        }
    }

    signingConfigs {

        debug {
            if(isGooglPlay&&isHwMetatel){
                keyAlias 'youyuan'
                keyPassword 'youyuan2025!'
                storeFile file('home/jenkins/youyuan.jks')
                storePassword 'youyuan2025!'
                v1SigningEnabled true
                v2SigningEnabled true
            }else if(isHwMetatel){
                keyAlias 'youyuan'
                keyPassword 'youyuan2025!'
                storeFile file('home/jenkins/youyuan.jks')
                storePassword 'youyuan2025!'
                v1SigningEnabled true
                v2SigningEnabled true
            }


        }

        release {
            if(isGooglPlay&&isHwMetatel){
                keyAlias 'youyuan'
                keyPassword 'youyuan2025!'
                storeFile file('home/jenkins/youyuan.jks')
                storePassword 'youyuan2025!'
                v1SigningEnabled true
                v2SigningEnabled true
            } else if(isHwMetatel){
                keyAlias 'youyuan'
                keyPassword 'youyuan2025!'
                storeFile file('home/jenkins/youyuan.jks')
                storePassword 'youyuan2025!'
                v1SigningEnabled true
                v2SigningEnabled true
            }



        }
    }
    buildTypes{
        //输出文件名称
        android.applicationVariants.all { variant ->
//            variant.outputs.all {
//                    // 输出apk名称为OY-AndroidMobile-2.2.232.214-201908201843-debug
//               outputFileName = isIoi ? "Ioi" :  "Metatel" + "-" + defaultConfig.versionName + "-" + new Date().format("yyMMddHHmm") + "-" + buildType.name + ".apk";
//            }
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', "proguard.cfg"
            signingConfig signingConfigs.release
        }

        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', "proguard.cfg"
            signingConfig signingConfigs.release
        }

    }
    packagingOptions {
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'

        exclude 'lib/arm64-v8a/libgojni.so'
        exclude 'lib/x86/libgojni.so'
        exclude 'lib/x86_64/libgojni.so'
    }

    aaptOptions {
        additionalParameters.add("--auto-add-overlay")
        ignoreAssetsPattern = "!.svn:!.git:.*:!CVS:!thumbs.db:!picasa.ini:!*.scc:*~"
    }

//    externalNativeBuild {
//        // Encapsulates your CMake build configurations.
//        cmake {
//            // Provides a relative path to your CMake build script.
//            path = file("../CMakeLists.txt")
//        }
//    }
}

flutter {
    source '../..'
}

dependencies {
//    configurations.all {
//        resolutionStrategy {
//            force 'androidx.webkit:webkit:1.4.0'
//        }
//    }


    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation 'androidx.activity:activity-ktx:1.8.0'
    implementation 'androidx.fragment:fragment-ktx:1.5.4'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation platform('com.google.firebase:firebase-bom:31.0.2')
    implementation 'com.google.firebase:firebase-installations-ktx:17.1.0'
    implementation 'io.github.happylishang:antifake:1.7.0'


    //uniapp集成
    implementation 'androidx.recyclerview:recyclerview:1.0.0' //必须集成，android 自带recyclerview支持
    implementation 'androidx.legacy:legacy-support-v4:1.0.0' //必须集成，androidx support支持
    implementation 'androidx.appcompat:appcompat:1.0.0' //必须集成，androidx appcompat支持
    implementation 'com.alibaba:fastjson:1.2.83' //必须集成，fastjson功能需要
    implementation 'com.facebook.fresco:fresco:2.5.0'//必须集成，图片加载需要
    implementation 'com.facebook.fresco:animated-gif:2.5.0'//必须集成，图片加载需要
    implementation 'com.github.bumptech.glide:glide:4.9.0'//必须集成，图片加载需要
    implementation 'androidx.webkit:webkit:1.5.0' //4.45版本之后 必须集成，用来支持暗黑模式
    // 重要：避免部分设备可能卡死的问题
    annotationProcessor 'com.github.bumptech.glide:compiler:4.9.0'

    //支付宝
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.7.9'
    api 'com.alipay.sdk:alipaysdk-android:+@aar'

}
tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}