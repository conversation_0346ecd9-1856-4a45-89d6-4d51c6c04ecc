import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../r.dart';

class ScrollStyleDialog extends StatelessWidget {
  const ScrollStyleDialog({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        /// Dialog内容部分
        Padding(
          padding: EdgeInsets.only(top: 4.r),
          child: Container(
            width: 380.r,
            constraints: BoxConstraints(minHeight: 264.r),
            decoration: BoxDecoration(
              image: DecorationImage(image: AssetImage(R.scrollStyleDialogBg), fit: BoxFit.fill),
            ),
            child: child,
          ),
        ),

        /// 关闭按钮
        Positioned(
          top: 0,
          right: 17.33.r,
          child: GestureDetector(
            onTap: () => Get.back(),
            child: Image.asset(
              R.scrollStyleDialogExit,
              width: 77.34.r,
              height: 59.11.r,
            ),
          ),
        ),
      ],
    );
  }
}