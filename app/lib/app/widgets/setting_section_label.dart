import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/values/colors.dart';

class SettingSectionLabel extends StatelessWidget {
  const SettingSectionLabel({super.key, this.label});

  final String? label;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 302.r,
      child: DottedBorder(
        customPath: (size) => Path()
          ..moveTo(0, size.height)
          ..relativeLineTo(size.width, 0),
        color: AppColors.colorFFCAB692,
        strokeWidth: 2,
        dashPattern: [5, 4],
        padding: EdgeInsets.symmetric(vertical: 6.r, horizontal: 15.r),
        child: label == null
          ? SizedBox.shrink()
          : Text(
              label!,
              style: TextStyle(
                letterSpacing: 1.2,
                fontSize: 20.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.colorFF6E5F4E,
              ),
            ),
      ),
    );
  }
}