import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/values/colors.dart';
import '../../r.dart';

class WoodenSettingItem extends StatelessWidget {
  const WoodenSettingItem(this.text, {super.key, this.image, this.showSuffixArrow = true, this.onTap, this.suffix, this.imageSize, this.woodenBg});
  final String text;
  final String? image;
  final String? woodenBg;
  final Size? imageSize;
  final bool showSuffixArrow;
  final Function()? onTap;
  final Widget? suffix;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 326.37.r,
        height: 61.56.r,
        padding: EdgeInsets.only(left: 22.71.r, right: 28.06.r),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(woodenBg ?? R.woodenSettingItemBg),
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          children: [
            if(image != null)
              Image.asset(
                image!,
                width: imageSize != null ? imageSize!.width : 16.r,
                height:  imageSize != null ? imageSize!.height : 16.r,
                fit: BoxFit.fill,
                color: AppColors.colorFFC2AF8C,
              ),
            if(image != null)
              SizedBox(width: 20.91.r),
            Text(
              text,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.colorFFC2AF8C,
              ),
            ),
            Spacer(),
            if(suffix != null)
              suffix!,
            if(suffix != null && showSuffixArrow)
              SizedBox(width: 13..r),
            if(showSuffixArrow)
              Image.asset(
                R.nextArrowGrey,
                width: 5.r,
                height: 10.r,
                color: AppColors.colorFFC2AF8C,
              ),
          ],
        ),
      ),
    );
  }
}