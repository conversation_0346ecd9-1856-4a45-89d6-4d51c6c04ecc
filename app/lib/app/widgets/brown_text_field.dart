import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/values/colors.dart';

class BrownTextField extends StatefulWidget {
  const BrownTextField({
    super.key,
    this.label,
    this.hintText,
    this.inputFormatters,
    this.maxLength,
    this.controller,
    this.suffixIcon,
    this.prefixIcon,
    this.textAlignVertical,
    this.focusNode,
    this.labelStyle,
    this.keyboardType,
    this.readOnly = false,
    this.textInputAction,
    this.textFieldWidth,
    this.maxLines = 1,
    this.showCounterText = false,
    this.textFieldHeight,
    this.obscureText = false,
    this.contentPadding,
    this.onChanged,
    this.textFieldAlignment,
    this.containerPadding,
    this.maxLengthEnforcement,
    this.description,
  });
  final double? textFieldWidth;
  final double? textFieldHeight;
  final String? label;
  final TextStyle? labelStyle;
  final String? hintText;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLength;
  final int? maxLines;
  final TextEditingController? controller;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final TextAlignVertical? textAlignVertical;
  final FocusNode? focusNode;
  final TextInputType? keyboardType;
  final bool readOnly;
  final TextInputAction? textInputAction;
  final bool showCounterText;
  final bool obscureText;
  final EdgeInsetsGeometry? contentPadding;
  final Function(String)? onChanged;
  final AlignmentGeometry? textFieldAlignment;
  final EdgeInsetsGeometry? containerPadding;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final Widget? description;

  @override
  State<BrownTextField> createState() => _BrownTextFieldState();
}

class _BrownTextFieldState extends State<BrownTextField> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.textFieldWidth ?? 300.r,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.label != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10).r,
              child: Text(
                widget.label!,
                style: widget.labelStyle ??
                    TextStyle(
                      height: 1.3,
                      fontSize: 17.sp,
                      color: AppColors.colorFF6E5F4E,
                      fontWeight: FontWeight.w500,
                      // fontFamily: Config.secondFontFamily,
                    ),
              ),
            ),
          if (widget.label != null) SizedBox(height: 5.r),
          Container(
            height: widget.textFieldHeight ?? 45.r,
            width: widget.textFieldWidth ?? 300.r,
            // padding: const EdgeInsets.only(
            //   left: 15,
            // ).r,
            padding: widget.containerPadding,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5).r),
              gradient: LinearGradient(
                colors: [
                  AppColors.colorFFC2AF8C,
                  AppColors.colorFF83623E,
                ],
              ),
            ),
            child: Align(
              alignment: widget.textFieldAlignment ?? Alignment.center,
              child: TextFormField(
                obscureText: widget.obscureText,
                readOnly: widget.readOnly,
                focusNode: widget.focusNode,
                controller: widget.controller,
                inputFormatters: widget.inputFormatters,
                maxLength: widget.maxLength,
                maxLengthEnforcement: widget.maxLengthEnforcement,
                maxLines: widget.maxLines,
                keyboardType: widget.keyboardType,
                textAlignVertical: widget.textAlignVertical,
                cursorColor: AppColors.colorFF83623E,
                textInputAction: widget.textInputAction,
                style: TextStyle(
                  fontSize: 17.sp,
                  color: AppColors.colorFF28231A,
                  fontWeight: FontWeight.w500,
                  // fontFamily: Config.secondFontFamily,
                ),
                // validator: widget.validator,
                // forceErrorText: null,
                onChanged: (value) {
                  widget.onChanged?.call(value);
                  setState(() {});
                },
                decoration: InputDecoration(
                  contentPadding: widget.contentPadding ?? EdgeInsets.only(left: 15.r, right: 15.r, top: 0, bottom: 0),
                  isDense: true,
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.colorFFE6E1DD,
                    fontWeight: FontWeight.w300,
                    // fontFamily: Config.secondFontFamily,
                  ),
                  counterText: '',
                  prefixIcon: widget.prefixIcon,
                  suffixIcon: widget.suffixIcon,
                  // filled: true,
                  // fillColor: AppColors.colorFF151A1A,
                  border: const OutlineInputBorder(
                    borderSide: BorderSide.none,
                    // borderRadius: BorderRadius.all(Radius.circular(25)),
                  ),
                  errorStyle: TextStyle(height: 0.01, color: Colors.transparent),
                ),
              ),
            ),
          ),
          if (widget.showCounterText || widget.description != null) SizedBox(height: 8.r),
          Row(
            children: [
              if (widget.description != null)
                Padding(
                  padding: const EdgeInsets.only(left: 13).r,
                  child: widget.description!,
                ),
              const Spacer(),
              if (widget.showCounterText)
                Padding(
                  padding: const EdgeInsets.only(right: 15).r,
                  child: Text(
                    "${widget.controller?.text.length ?? 0}/${widget.maxLength}",
                    style: TextStyle(
                      height: 1.3,
                      fontSize: 15.sp,
                      color: AppColors.colorFF6E5F4E,
                      fontWeight: FontWeight.w500,
                      // fontFamily: Config.secondFontFamily,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
