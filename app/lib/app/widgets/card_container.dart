import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/shape_border/inverted_corner_border.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CardContainer extends StatelessWidget {
  const CardContainer({super.key, 
    this.frameColor = AppColors.colorFFE0D1BE, 
    this.frameBorderColor = AppColors.colorFFD5BFA7, 
    this.bodyColor = AppColors.colorFFF5F2ED, 
    this.bodyBorderColor = AppColors.colorFFCAB692, 
    this.child,
  });
  final Color frameColor;
  final Color frameBorderColor;
  final Color bodyColor;
  final Color bodyBorderColor;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 410.r,
      constraints: BoxConstraints(minHeight: 173.r),
      padding: EdgeInsets.all(8.r),
      decoration: BoxDecoration(
        color: frameColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: frameBorderColor, width: 1.5.r),
      ),
      child: Container(
        decoration: ShapeDecoration(
          color: bodyColor.withOpacity(0.8),
          shape: InvertedCornerBorder(radius: 10.r, pathWidth: 1.5.r),
          // border: Border.all(color: bodyBorderColor, width: 1.5.r),
        ),
        child: child,
      ),
    );
  }
}