import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/values/colors.dart';
import '../../r.dart';

class AvatarFrame extends StatelessWidget {
  const AvatarFrame({super.key, this.width, this.height, required this.child, this.boxShadow});
  final double? width;
  final double? height;
  final List<BoxShadow>? boxShadow;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? 98.57.r,
      height: height ?? 101.95.r,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        image: DecorationImage(
          image: AssetImage(R.meAvatarBg),
          fit: BoxFit.fill,
        ),
        boxShadow: boxShadow ?? [
          BoxShadow(
            color: AppColors.color73251F21,
            blurRadius: 4.r,
            offset: Offset(4.r, 1.r),
          ),
        ],
      ),
      child: child,
    );
  }
}