import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:gradient_borders/gradient_borders.dart';

import '../../core/values/colors.dart';
import '../../r.dart';

class GradientProgressBar extends StatelessWidget {
  final double value; // 进度值 (0.0 ~ 1.0)
  final double height; // 进度条高度
  final double borderRadius; // 圆角半径
  final double borderWidth; // 边框宽度
  final List<Color> borderColor; // 边框颜色
  final List<Color> gradientColors; // 渐变颜色

  const GradientProgressBar({
    super.key,
    required this.value,
    this.height = 16,
    this.borderRadius = 14,
    this.borderWidth = 0.5,
    this.borderColor = const [AppColors.colorFFC2AF8C, AppColors.colorFF6E5F4E],
    this.gradientColors = const [AppColors.colorFFC2AF8C, AppColors.colorFF6E5F4E],
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            // 背景边框层
            Center(
              child: Container(
                width: 312.r,
                height: 35.r,
              decoration: BoxDecoration(
                  // border: GradientBoxBorder(
                  //   gradient: LinearGradient(
                  //     colors: borderColor,
                  //   ),
                  //   width: borderWidth.r, // 边框宽度
                  // ),
                  // borderRadius: BorderRadius.circular(borderRadius).r,
                  image: DecorationImage(
                    image: AssetImage(R.progressBarBg),
                    fit: BoxFit.fill,
                  ),
                ),
              ),
            ),

            // 渐变填充层
            Positioned(
              left: 15.r,
              top: 6.5.r,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(9).r,
                child: Container(
                  width: 282.r  * value.clamp(0.0, 1.0),
                  height: 22.r,
                  decoration: BoxDecoration(
                    // border: GradientBoxBorder(
                    //   gradient: LinearGradient(
                    //     colors: borderColor,
                    //   ),
                    //   width: borderWidth.r, // 边框宽度
                    // ),
                    borderRadius: BorderRadius.circular(9).r,
                    image: DecorationImage(
                      image: AssetImage(R.progressBarProgress),
                      fit: BoxFit.fill,
                  ),
                  ),
                ),
              ),
            ),
            // ClipRRect(
            //   borderRadius: BorderRadius.circular(17).r,
            //   child: Container(
            //     padding: EdgeInsets.symmetric(horizontal:10.r, vertical: 7.r),
            //     height: 35.r,
            //     width: constraints.maxWidth * value.clamp(0.0, 1.0),
            //     child: Container(
            //       decoration: BoxDecoration(
            //         gradient: LinearGradient(
            //           colors: gradientColors,
            //           begin: Alignment.centerLeft,
            //           end: Alignment.centerRight,
            //         ),
            //         borderRadius: BorderRadius.circular(borderRadius).r,
            //       ),

            //     ),
            //   ),
            // ),
          ],
        );
      },
    );
  }
}