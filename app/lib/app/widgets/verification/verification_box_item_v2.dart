import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../r.dart';

class VerificationBoxItemV2 extends StatelessWidget {
  const VerificationBoxItemV2({super.key, this.isFilled = false, this.obscureText = true});
  final bool isFilled;
  final bool obscureText;

  @override
  Widget build(BuildContext context) {
    return AnimatedRotation(
      duration: const Duration(milliseconds: 300),
      turns: isFilled ? 1/8 : 0, // 1/8 = 45 degree; 1 = 360 degree
      child: Container(
        width: 20.85.r,
        height: 20.85.r,
        child: isFilled 
        ? Image.asset(
            R.pinItemFill,
            fit: BoxFit.fill,
          )
        : Image.asset(
            R.pinItem,
            fit: BoxFit.fill,
          ),
      ),
    );
  }
}