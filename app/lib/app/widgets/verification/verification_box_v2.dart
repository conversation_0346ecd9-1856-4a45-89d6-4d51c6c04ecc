import 'package:flutter/cupertino.dart';
import 'package:flutter/scheduler.dart';
// import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/utils/app_log.dart';
import '../../../r.dart';
import 'verification_box_item_v2.dart';

typedef OnSubmitted = void Function(String text, Function? clear);
GlobalKey<VerificationBoxV2State> verificationBoxKey04 = GlobalKey<VerificationBoxV2State>();
GlobalKey<VerificationBoxV2State> verificationBoxKey05 = GlobalKey<VerificationBoxV2State>();
GlobalKey<VerificationBoxV2State> verificationBoxKey06 = GlobalKey<VerificationBoxV2State>();

class VerificationBoxV2 extends StatefulWidget {
  const VerificationBoxV2({
    super.key, this.count = 6, this.textEditingController, 
      this.onSubmitted, this.onTap, this.obscureText = true, this.autoFocus = false, this.unfocus = true, this.withBg = true});
  /// 几位验证码，一般6位，还有4位的
  final int count;

  final TextEditingController? textEditingController;

  /// 输入完成回调
  final OnSubmitted? onSubmitted;

  // final PointerDownEventListener? onTap;
  final Function()? onTap;

  final bool obscureText;

  /// 是否自动获取焦点
  final bool autoFocus;

  /// 输入完成后是否失去焦点，默认true，失去焦点后，软键盘消失
  final bool unfocus;

  final bool withBg;

  @override
  State<VerificationBoxV2> createState() => VerificationBoxV2State();
}

class VerificationBoxV2State extends State<VerificationBoxV2> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  final List _contentList = [];

  @override
  void initState() {
    List.generate(widget.count, (index) {
      _contentList.add('');
    });
    _controller = widget.textEditingController??TextEditingController();
    _focusNode = FocusNode();
    SchedulerBinding.instance.addPostFrameCallback(
      (d) {
        if(widget.autoFocus){
          _focusNode.requestFocus();
          setState(() {});
        }
      }
    );
    super.initState();
  }

  // clearData() {
  //   for (int i = 0; i < widget.count; i++) {
  //     _contentList[i] = '';
  //   }
  //   _controller.text = '';
  //   setState(() {});
  // }
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap ?? () {
          FocusScope.of(context).requestFocus(_focusNode);
        },
      child: Container(
        width: 328.19.r,
        height: widget.withBg ? 125.26.r : 30.r,
        padding: EdgeInsets.symmetric(horizontal: 52.r),
        decoration: BoxDecoration(
          image: !widget.withBg 
          ? null
          : DecorationImage(
            image: AssetImage(R.pinBg),
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            for(var i=0; i<widget.count; i++)
              VerificationBoxItemV2(isFilled: _controller.text.length > i ? true : false),
          ],
        ),
      ),
    );
  }

  setFocus(bool focus){
    if(focus){
      _focusNode.requestFocus();
    } else {
      if (widget.unfocus) _focusNode.unfocus();
    }
    setState(() {});
  }

  onValueChange(value) {
    AppLogger.d('showSecureKeyBoard onValueChange value=$value');
    // widget.onChanged?.call(value);
    if(value.toString().isNotEmpty){
      _focusNode.requestFocus();
    }
    for (int i = 0; i < widget.count; i++) {
      _controller.text = value;
      if (i < value.length) {
        _contentList[i] = value.substring(i, i + 1);
      } else {
        _contentList[i] = '';
      }
    }
    setState(() {});
    if (value.length == widget.count) {
      if (widget.unfocus) _focusNode.unfocus();

      widget.onSubmitted?.call(value, null);
    }
  }
}