import 'package:flutter/material.dart';

import '../../../core/values/colors.dart';

class InvertedCornerBorder extends ShapeBorder {
  final double radius;
  final double pathWidth;
  final Color borderColor;

  const InvertedCornerBorder({required this.radius, this.pathWidth = 3, this.borderColor = AppColors.colorFFCAB692});

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.zero;

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return _createPath(rect);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return _createPath(rect);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final path = _createPath(rect);
    final paint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = pathWidth;
    
    canvas.drawPath(path, paint);
  }

  @override
  ShapeBorder scale(double t) => InvertedCornerBorder(radius: radius);

  Path _createPath(Rect rect) {
    return Path.combine(
      PathOperation.difference, 
      Path()..addRect(rect),
      _createBevels(rect, radius),
    );
  }

  Path _createBevels(Rect rect, double radius) {
    return Path()
      ..addOval(
          Rect.fromCircle(center: Offset(rect.left, rect.top), radius: radius))
      ..addOval(Rect.fromCircle(
          center: Offset(rect.left + rect.width, rect.top), radius: radius))
      ..addOval(Rect.fromCircle(
          center: Offset(rect.left, rect.top + rect.height), radius: radius))
      ..addOval(Rect.fromCircle(
          center: Offset(rect.left + rect.width, rect.top + rect.height),
          radius: radius));
  }
}