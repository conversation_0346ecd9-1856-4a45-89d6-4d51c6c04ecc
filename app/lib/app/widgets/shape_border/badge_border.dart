import 'package:flutter/material.dart';

class BadgeBorder extends ShapeBorder {
  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.zero;

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return _createBadgePath(rect);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return _createBadgePath(rect);
  }

  Path _createBadgePath(Rect rect) {
    final path = Path();
    final width = rect.width;
    final height = rect.height;
    final topHalfHeight = height * 0.12;
    final btmHalfHeight = height * 0.75;

    /// Top left curve
    path.quadraticBezierTo((width / 2) / 2, topHalfHeight / 3, width / 2, topHalfHeight);

    /// Top right curve
    path.quadraticBezierTo(width - ((width / 2) / 2), (topHalfHeight) / 3, width, 0);

    /// Right Curve
    path.quadraticBezierTo(width * 0.9, height / 3, width, btmHalfHeight);

    /// Bottom center point
    path.lineTo(width / 2, height);

    /// Bottom left point
    path.lineTo(0, btmHalfHeight);

    /// Left Curve
    path.quadraticBezierTo(width * 0.1, height / 3, 0, 0);

    path.close();

    return path;
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {}

  @override
  ShapeBorder scale(double t) => this;
}
