import 'package:flutter/material.dart';

class BadgeClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    // final path = Path();
    // path.moveTo(size.width / 2, 0);
    // path.lineTo(size.width, size.height);
    // path.lineTo(0, size.height);
    // path.close();
    return _createBadgePath(size);
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;

  Path _createBadgePath(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;
    final topHalfHeight = height * 0.12;
    final btmHalfHeight = height * 0.75;

    /// Top left curve
    path.quadraticBezierTo((width / 2) / 2, topHalfHeight / 3, width / 2, topHalfHeight);

    /// Top right curve
    path.quadraticBezierTo(width - ((width / 2) / 2), (topHalfHeight) / 3, width, 0);

    /// Right Curve
    path.quadraticBezierTo(width * 0.9, height / 3, width, btmHalfHeight);

    /// Bottom center point
    path.lineTo(width / 2, height);

    /// Bottom left point
    path.lineTo(0, btmHalfHeight);

    /// Left Curve
    path.quadraticBezierTo(width * 0.1, height / 3, 0, 0);
    path.close();

    return path;
  }
}

// 使用
// Shadow(
//   blurRadius: 15,
//   color: Colors.black.withOpacity(0.5),
//   offset: Offset(5, 5),
//   child: ClipPath(
//     clipper: TriangleClipper(),
//     child: Image.asset('assets/your_image.png'),
//   ),
// )
