import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class GradientButtonNotClick extends StatelessWidget {
  final Widget child;
  double? width;
  double? height;
  Rx<Gradient> gradient = const LinearGradient(
    colors: [
      AppColors.color29000000,
      AppColors.color29000000,
    ],
  ).obs;

  GradientButtonNotClick({
    super.key,
    required this.child,
    this.width,
    this.height,
    Rx<Gradient>? gradient,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Obx(() {
        return Container(
          width: width ?? 226.r,
          height: height ?? 36.r,
          decoration: BoxDecoration(
            gradient: gradient.value,
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: Center(
            child: child,
          ),
        );
      }),
    );
  }
}
