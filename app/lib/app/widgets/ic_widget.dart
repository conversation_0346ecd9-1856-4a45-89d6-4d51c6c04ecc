import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../core/utils/util.dart';
import '../../r.dart';


class IcWidget extends StatelessWidget {
  IcWidget(
      {super.key,
      required this.isNet,
      this.diameter = 36,
      this.chainid,
      this.filePath,
      this.asset,
      this.width,
      this.height,
      this.symbol,
      this.isMain = false,
      this.uuid = ''});

  String? filePath;
  String? asset;
  double? width;
  double? height;
  String? symbol;
  bool isMain = false;
  String uuid = '';
  int? chainid;
  bool isNet;//网络icon

  /// 直径 ///不要进行屏幕适配，控件自己已经适配
  final double diameter;
  Rx<Widget> icWidget = Rx<Widget>(Container());

  @override
  Widget build(BuildContext context) {
    var image;
    if ((filePath?.isNotEmpty ?? false)) {
      if(filePath!.startsWith("assets/images")){
        image = AssetImage(filePath ?? '',);
      }else if(filePath!.startsWith("http")) {
        icWidget.value = CachedNetworkImage(
          imageUrl: image ?? '',
          width: diameter.r/2,
          height: diameter.r/2,
          fit: BoxFit.cover,
          errorWidget: (c, e, s) {
            return Image.asset(
              getDefultAset(),
              width: diameter.r/2,
              height: diameter.r/2,
            );
          },
        );
      } else{
        filePath = getIcAbsolutePath(filePath ?? '');
        if (File(filePath ?? '').existsSync()) {
          image = FileImage(File(filePath!), scale: 8);
        }
      }

    } else {
      if (asset != null && asset!.isNotEmpty) {
        image = AssetImage(asset ?? '', );
      }
    }
    // walletLog('IcWidget image =$image');
    image ??= AssetImage(getDefultAset(), );
    icWidget.value = CircleAvatar(
      radius: diameter.r / 2,
      backgroundColor: Colors.white,
      backgroundImage: image,
      onBackgroundImageError: (e, s) {
        // walletLog('IcWidget image =${e.toString()} s= ${s.toString()}');
        Future.delayed(
            Duration.zero, () => icWidget.value = _loadDefaultWidget());
      },
    );
    return Obx(
      () => Container(
        child: icWidget.value,
      ),
    );
  }

  String getDefultAset() {
    var asetImage = R.icoDefault;
    if (isNet) {
      if (chainid != null) {
        asetImage = netWorkIconByChatId(chainid!);
      }
    } else {
      if(chainid!=null){
        asetImage = iconBySymbolOrChainId(symbol ?? '', isMain: isMain,chainid: chainid);
      } else{
        asetImage = iconBySymbolOrNetUuid(symbol ?? '', isMain: isMain, uuid: uuid);
      }
    }

    return asetImage;
  }

  Widget _loadDefaultWidget() {
    return Image.asset(
      getDefultAset(),
      width: diameter.r / 2,
      height: diameter.r / 2,
    );
  }
}
