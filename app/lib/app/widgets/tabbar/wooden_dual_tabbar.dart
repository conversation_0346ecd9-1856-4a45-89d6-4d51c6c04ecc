import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/mask/blend_mask.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/values/colors.dart';
import '../../../r.dart';

class WoodenDualTabbar extends StatefulWidget {
  const WoodenDualTabbar({
    super.key,
    required this.tab1,
    required this.tab2,
    this.onTab1Tap,
    this.onTab2Tap,
  });
  final String tab1;
  final String tab2;
  final Function()? onTab1Tap;
  final Function()? onTab2Tap;

  @override
  State<WoodenDualTabbar> createState() => _WoodenDualTabbarState();
}

class _WoodenDualTabbarState extends State<WoodenDualTabbar> {
  int _currentTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 378.r,
      height: 89.35.r,
      child: Stack(
        children: [
          /// 旗子
          Positioned(
            left: _currentTabIndex == 0 ? 10 : 378.r / 2,
            bottom: 10,
            child: Image.asset(
              R.tabbarFlag,
              width: 80.r,
              // height: 80.r,
              fit: BoxFit.fill,
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Row(
              children: [
                WoodenDualTabbarTab(
                  type: 1,
                  label: widget.tab1,
                  isSelected: _currentTabIndex == 0,
                  onTap: () {
                    widget.onTab1Tap?.call();
                    _currentTabIndex = 0;
                    setState(() {});
                  },
                ),
                WoodenDualTabbarTab(
                    type: 2,
                    label: widget.tab2,
                    isSelected: _currentTabIndex == 1,
                    onTap: () {
                      widget.onTab2Tap?.call();
                      _currentTabIndex = 1;
                      setState(() {});
                    }),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class WoodenDualTabbarTab extends StatelessWidget {
  const WoodenDualTabbarTab({super.key, required this.type, required this.label, required this.isSelected, this.onTap});
  final int type; // 1: 左 2: 右
  final String label;
  final bool isSelected;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 378.r / 2,
        height: 44.r,
        child: Stack(
          children: [
            /// Tabbar背景图
            Image.asset(
              type == 1 ? R.tabbarWoodenLeftBg : R.tabbarWoodenRightBg,
              width: 378.r / 2,
              height: 44.r,
              fit: BoxFit.fill,
            ),

            /// 遮挡
            if (!isSelected)
              Align(
                alignment: type == 1 ? Alignment.centerRight : Alignment.centerLeft,
                child: BlendMask(
                  blendMode: BlendMode.multiply,
                  opacity: 0.7,
                  child: Image.asset(
                    type == 1 ? R.tabbarBlockerLeft : R.tabbarBlockerRight,
                    width: 182.3.r,
                    height: 34.2.r,
                    fit: BoxFit.fill,
                  ),
                ),
              ),

            /// 标签
            Center(
              child: Text(
                label,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 17.sp,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                  color: isSelected ? AppColors.colorFFD19832 : AppColors.colorFFA1804E,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
