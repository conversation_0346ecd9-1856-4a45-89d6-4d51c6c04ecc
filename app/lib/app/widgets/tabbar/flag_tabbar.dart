import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/values/colors.dart';
import '../../../r.dart';

class FlagTabbar extends StatefulWidget {
  const FlagTabbar({super.key,this.tabController, required this.tabs});
  final TabController? tabController;
  final List<String> tabs;

  @override
  State<FlagTabbar> createState() => _FlagTabbarState();
}

class _FlagTabbarState extends State<FlagTabbar> {
  late final _tabController = widget.tabController;
  bool _swipeIsInProgress = false;
  bool _tapIsBeingExecuted = false;
  int _currentTabIndex = 0;
  int _prevIndex = 0;
    
  @override
  void initState() {
    if(_tabController != null){
      _tabController!.animation?.addListener(_tabAnimationListener);
      _tabController!.addListener(_tabListener);
    }
    super.initState();
  }

  @override
  void dispose() {
    _tabController?.animation?.removeListener(_tabAnimationListener);
    _tabController?.removeListener(_tabListener);
    super.dispose();
  }

  void _tabAnimationListener() {
    if(_tabController == null) return;
    if (!_tapIsBeingExecuted && !_swipeIsInProgress && (_tabController!.offset >= 0.5 || _tabController!.offset <= -0.5)) {
      // detects if a swipe is being executed. limits set to 0.5 and -0.5 to make sure the swipe gesture triggered
      int newIndex = _tabController!.offset > 0 ? _tabController!.index + 1 : _tabController!.index - 1;
      _swipeIsInProgress = true;
      _prevIndex = _currentTabIndex;
      setState(() {
        _currentTabIndex = newIndex;
      });
    } else {
      if (!_tapIsBeingExecuted && _swipeIsInProgress && ((_tabController!.offset < 0.5 && _tabController!.offset > 0) ||
              (_tabController!.offset > -0.5 && _tabController!.offset < 0))) {
        // detects if a swipe is being reversed.
        _swipeIsInProgress = false;
        setState(() {
          _currentTabIndex = _prevIndex;
        });
      }
    }
  }

  void _tabListener() {
    if(_tabController == null) return;
    _swipeIsInProgress = false;
    setState(() {
      _currentTabIndex = _tabController!.index;
    });
    if (_tapIsBeingExecuted == true) {
      _tapIsBeingExecuted = false;
    } else {
      if (_tabController!.indexIsChanging) {
        // this is only true when the tab is changed via tap
        _tapIsBeingExecuted = true;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 415.r,
      height: 84.25.r,
      child: widget.tabController == null 
        ? SizedBox.shrink()
        : Stack(
            children: [
              /// 杆
              Image.asset(
                R.meetingTabBar,
                width: 415.r,
                height: 18.5.r,
                fit: BoxFit.fill,
              ),

              /// 旗帜部分
              Align(
                alignment: Alignment.topCenter,
                child: Container(
                  height: 79.55.r,
                  width: 314.35.r,
                  margin: EdgeInsets.only(top: 4.7.r),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      for(var index=0;index<widget.tabs.length;index++)
                        GestureDetector(
                          onTap: () {
                            if(_tabController == null) return;
                            _tabController!.animateTo(index);
                          },
                          child: Container(
                            height: _currentTabIndex == index ?  79.55.r : 63.74.r,
                            width: 88.74.r,
                            // constraints: BoxConstraints(minWidth: 91.r),
                            padding: EdgeInsets.symmetric(horizontal: 22.r),
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage(_currentTabIndex == index ? R.meetingTabActive : R.meetingTab),
                                fit: BoxFit.fill,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                widget.tabs[index],
                                textAlign: TextAlign.center,
                                style: _currentTabIndex == index 
                                  ? TextStyle(
                                      height: 1.2,
                                      fontSize: 17.sp,
                                      fontWeight: FontWeight.w500,
                                      color: AppColors.colorFF6D0000,
                                    )
                                  : TextStyle(
                                      height: 1.2,
                                      fontSize: 15.sp,
                                      fontWeight: FontWeight.w500,
                                      color: AppColors.colorFFEEEBE3,
                                    ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
    );
    // Container(

    //         children: [
    //                 child: Row(
    //                   children: [
    //                     SizedBox(width: 19.r),
    //                     /// Tabbar的Tab按钮
    //                     for(var index=0;index<widget.tabs.length;index++)
    //                       GestureDetector(
    //                         onTap: () {
    //                           if(_tabController == null) return;
    //                           _tabController!.animateTo(index);
    //                         },
    //                         child: Container(
    //                           height: 42.r,
    //                           constraints: BoxConstraints(minWidth: 91.r),
    //                           padding: EdgeInsets.symmetric(horizontal: 0.06.sw),
    //                           decoration: BoxDecoration(
    //                             image: DecorationImage(
    //                               image: AssetImage(_currentTabIndex == index ? R.tabbarTabActiveBg : R.tabbarTabBg),
    //                               fit: BoxFit.fill,
    //                             ),
    //                           ),
    //                           child: Center(
    //                             child: Text(
    //                               widget.tabs[index],
    //                               textAlign: TextAlign.center,
    //                               style: TextStyle(
    //                                 fontSize: 15.sp,
    //                                 fontWeight: FontWeight.w500,
    //                                 color: AppColors.colorFFC2AF8C
    //                               ),
    //                             ),
    //                           ),
    //                         ),
    //                       ),
                
    //                     SizedBox(width: 19.r),
    //                   ],
    //                 ),
    //               ),
    //           ),
    //         ],
    //       ),
    // );
  }
}