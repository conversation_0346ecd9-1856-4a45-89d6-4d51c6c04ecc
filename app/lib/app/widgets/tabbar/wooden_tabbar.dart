import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/values/colors.dart';
import '../../../r.dart';

class WoodenTabbar extends StatefulWidget {
  const WoodenTabbar({super.key,this.tabController, required this.tabs, this.withBg = true});
  final TabController? tabController;
  final List<String> tabs; 
  final bool withBg;

  @override
  State<WoodenTabbar> createState() => _WoodenTabbarState();
}

class _WoodenTabbarState extends State<WoodenTabbar> {
  late final _tabController = widget.tabController;
  bool _swipeIsInProgress = false;
  bool _tapIsBeingExecuted = false;
  int _currentTabIndex = 0;
  int _prevIndex = 0;
    
  @override
  void initState() {
    if(_tabController != null){
      _tabController!.animation?.addListener(_tabAnimationListener);
      _tabController!.addListener(_tabListener);
    }
    super.initState();
  }

  @override
  void dispose() {
    _tabController?.animation?.removeListener(_tabAnimationListener);
    _tabController?.removeListener(_tabListener);
    super.dispose();
  }

  void _tabAnimationListener() {
    if(_tabController == null) return;
    if (!_tapIsBeingExecuted && !_swipeIsInProgress && (_tabController!.offset >= 0.5 || _tabController!.offset <= -0.5)) {
      // detects if a swipe is being executed. limits set to 0.5 and -0.5 to make sure the swipe gesture triggered
      int newIndex = _tabController!.offset > 0 ? _tabController!.index + 1 : _tabController!.index - 1;
      _swipeIsInProgress = true;
      _prevIndex = _currentTabIndex;
      setState(() {
        _currentTabIndex = newIndex;
      });
    } else {
      if (!_tapIsBeingExecuted && _swipeIsInProgress && ((_tabController!.offset < 0.5 && _tabController!.offset > 0) ||
              (_tabController!.offset > -0.5 && _tabController!.offset < 0))) {
        // detects if a swipe is being reversed.
        _swipeIsInProgress = false;
        setState(() {
          _currentTabIndex = _prevIndex;
        });
      }
    }
  }

  void _tabListener() {
    if(_tabController == null) return;
    _swipeIsInProgress = false;
    setState(() {
      _currentTabIndex = _tabController!.index;
    });
    if (_tapIsBeingExecuted == true) {
      _tapIsBeingExecuted = false;
    } else {
      if (_tabController!.indexIsChanging) {
        // this is only true when the tab is changed via tap
        _tapIsBeingExecuted = true;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: widget.withBg ? 76.r : 50.r,
      decoration: widget.withBg 
        ? BoxDecoration(
            image: DecorationImage(
              image: AssetImage(R.tabbarBg),
              fit: BoxFit.fill,
            ),
          )
        : null,
      child: widget.tabController == null 
        ? SizedBox.shrink()
        : Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 8.r),
              SizedBox(
                height: 42.r,
                child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    physics: const BouncingScrollPhysics(),
                    child: Row(
                      children: [
                        SizedBox(width: 19.r),
                        /// Tabbar的Tab按钮
                        for(var index=0;index<widget.tabs.length;index++)
                          GestureDetector(
                            onTap: () {
                              if(_tabController == null) return;
                              _tabController!.animateTo(index);
                            },
                            child: Container(
                              height: 42.r,
                              constraints: BoxConstraints(minWidth: 91.r),
                              padding: EdgeInsets.symmetric(horizontal: 0.06.sw),
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(_currentTabIndex == index ? R.tabbarTabActiveBg : R.tabbarTabBg),
                                  fit: BoxFit.fill,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  widget.tabs[index],
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.colorFFC2AF8C
                                  ),
                                ),
                              ),
                            ),
                          ),
                
                        SizedBox(width: 19.r),
                      ],
                    ),
                  ),
              ),
            ],
          ),
    );
  }
}