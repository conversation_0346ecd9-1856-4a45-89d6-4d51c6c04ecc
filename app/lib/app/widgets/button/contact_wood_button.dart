import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/values/colors.dart';
import '../../../r.dart';

class ContactWoodButton extends StatelessWidget {
  const ContactWoodButton({super.key, required this.icon, required this.label, this.onTap});
  final String icon;
  final String label;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 78.77.r,
            height: 83.42.r,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(R.contactDetailButton),
                fit: BoxFit.fill,
              ),
            ),
            child: Center(
              child: Image.asset(icon, width: 18.r, height: 18.r),
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 15.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.colorFF6E5F4E,
            ),
          ),
        ],
      ),
    );
  }
}
