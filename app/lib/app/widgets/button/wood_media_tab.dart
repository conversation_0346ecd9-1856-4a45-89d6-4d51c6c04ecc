import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/values/colors.dart';
import '../../../r.dart';

class WoodMediaTab extends StatelessWidget {
  const WoodMediaTab({super.key, this.isSelected = false, required this.label, this.onTap});
  final bool isSelected;
  final String label;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 74.56.r,
        height: 39.63.r,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(isSelected ? R.contactDetailTabActive : R.contactDetailTab),
            fit: BoxFit.fill,
          ),
        ),
        child: Center(
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              color: isSelected ? AppColors.colorFF83623E : AppColors.colorFF4C3C28,
            ),
          ),
        ),
      ),
    );
  }
}
