import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../core/values/colors.dart';

class BrownAppBar extends StatelessWidget {
  const BrownAppBar(
      {super.key,
      required this.child,
      this.title,
      this.resizeToAvoidBottomInset = true,
      this.onBack,
      this.onEmptySpaceTap,
      this.suffix,
      this.bgColor,
      this.dottedColor,
      this.searchBar,
      this.prefix});
  final String? title;
  final bool resizeToAvoidBottomInset;
  final Widget child;
  final Widget? prefix;
  final Widget? suffix;
  final Color? bgColor;
  final Color? dottedColor;
  final Widget? searchBar;

  /// eg. 当有WithSecureKeyboard的页面退出和关闭键盘时可以使用
  final Function()? onBack;
  final Function()? onEmptySpaceTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onEmptySpaceTap?.call() ?? FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
        backgroundColor: AppColors.backgroundGray,
        body: SafeArea(
          bottom: false,
          child: Column(
            children: [
              Container(
                height: searchBar != null ? 148.r : 100.r,
                padding: EdgeInsets.all(10.r),
                decoration: BoxDecoration(
                  color: bgColor ?? AppColors.colorFF513725,
                  // image: DecorationImage(
                  //   image: AssetImage(R.homeAppbarBg),
                  //   fit: BoxFit.fill,
                  // ),
                ),
                child: DottedBorder(
                  color: dottedColor ?? AppColors.colorFF83623E,
                  dashPattern: [6, 3],
                  strokeWidth: 1.5,
                  padding: EdgeInsets.symmetric(horizontal: 7.r),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: onBack?.call ?? () => Get.back(),
                            child: Padding(
                              padding: const EdgeInsets.only(left: 5, right: 9, top: 9, bottom: 9).r,
                              child: Icon(
                                Icons.arrow_back_ios_new,
                                size: 14.r,
                                color: AppColors.colorFFC2AF8C,
                              ),
                            ),
                          ),
                          SizedBox(width: 4.r),
                          if (prefix != null) Expanded(child: prefix!),
                          if (prefix != null) SizedBox(width: 8.r),
                          if (title != null)
                            Expanded(
                              child: Text(
                                title!,
                                maxLines: 1,
                                style: TextStyle(
                                  fontSize: 20.sp,
                                  color: AppColors.colorFFCAB692,
                                  fontWeight: FontWeight.w500,
                                  letterSpacing: 1.2,
                                ),
                              ),
                            ),
                          if (suffix != null) SizedBox(width: 8.r),
                          if (suffix != null) suffix!,
                          if (suffix != null) SizedBox(width: 8.r),
                          // Image.asset(R.appbarDot, width: 10.r, height: 10.r, fit: BoxFit.fill),
                        ],
                      ),
                      if (searchBar != null) SizedBox(height: 13.r),
                      if (searchBar != null) searchBar!,
                    ],
                  ),
                ),
              ),
              Expanded(child: child),
            ],
          ),
        ),
      ),
    );
  }
}
