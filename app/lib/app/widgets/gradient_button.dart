import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class GradientButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  double? width;
  double? height;
  Rx<Gradient> gradient = const LinearGradient(
    colors: [
      AppColors.primaryBgColor2,
      AppColors.primaryBgColor1,
    ],
  ).obs;

  GradientButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.width,
    this.height,
    Rx<Gradient>? gradient,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (v) {
        gradient.value = const LinearGradient(
          colors: [
            Color.fromRGBO(22, 154, 164, 0.9),
            Color.fromRGBO(82, 183, 106, 0.9),
          ],
        );
      },
      onTapCancel: () {
        gradient.value = const LinearGradient(
          colors: [
            AppColors.primaryBgColor2,
            AppColors.primaryBgColor1,
          ],
        );
      },
      onTapUp: (v) {
        ItemClickFunction(
            startFunction: () {},
            endFunction: () {
              gradient.value = const LinearGradient(colors: [
                AppColors.primaryBgColor2,
                AppColors.primaryBgColor1,
              ]);
              onPressed?.call();
            });
      },
      child: Obx(() {
        return Container(
          width: width ?? 226.r,
          height: height ?? 36.r,
          decoration: BoxDecoration(
            gradient: gradient.value,
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: Center(
            child: child,
          ),
        );
      }),
    );
  }
}
