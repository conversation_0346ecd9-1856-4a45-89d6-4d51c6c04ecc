import 'package:flutter/material.dart';

class CreateAddMediaBtnBorder extends ShapeBorder{
  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.zero;

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return _createAddMediaBtnPath(rect);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return _createAddMediaBtnPath(rect);
  }

  Path _createAddMediaBtnPath(Rect rect) {
    final path = Path();
    path.moveTo(rect.left , rect.top);
    path.lineTo(rect.right - rect.width / 16, rect.top);
    path.lineTo(rect.right, rect.centerLeft.dy);
    path.lineTo(rect.right - rect.width / 16, rect.bottom);
    path.lineTo(rect.left, rect.bottom);
    // path.lineTo(rect.left, rect.centerLeft.dy);
    
    path.close();

    return path;
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
  }

  @override
  ShapeBorder scale(double t) => this;
}