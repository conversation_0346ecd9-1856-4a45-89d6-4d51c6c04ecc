import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/mask/blend_mask.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../../routes/pages.dart';
import '../../data/services/config_service.dart';
import '../../data/services/secure_store_service.dart';
import '../../widgets/button/wood_button.dart';
import '../../widgets/pdf_view.dart';

class AgreementView extends StatelessWidget {
  const AgreementView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      extendBody: true,
      body: Container(
        width: 1.sw,
        height: 1.sh,
        decoration: BoxDecoration(
          color: AppColors.white,
          image: DecorationImage(
            image: AssetImage(R.loginscreenBg),
            fit: BoxFit.cover,
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 92.r),
              Image.asset(
                R.logoBadge,
                width: 166.r,
              ),
              SizedBox(height: 17.r),
              AgreementBoard(),
              SizedBox(height: 17.r),
              // Text(
              //   L.user_agreement_and_privacy_policy_title.tr,
              //   style: TextStyle(
              //     fontSize: 18.sp,
              //     fontWeight: FontWeight.bold,
              //     color: AppColors.colorFF333333,
              //   ),
              // ),
              // SizedBox(height: 20.r),
              // Container(
            ],
          ),
        ),
      ),
    );
  }
}

class AgreementBoard extends StatelessWidget {
  const AgreementBoard({super.key});

  @override
  Widget build(BuildContext context) {
    var _agreementTitleHeight = 60.r;
    return Stack(
      children: [
        /// 板背景图
        Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: _agreementTitleHeight/2),
              Container(
                width: 376.r,
                // height: 541.r,
                padding: const EdgeInsets.only(top:65.32, left: 37.5, right: 37.5).r,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(R.agreementBg),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Column(
                  children: [
                    /// 协议内容
                    RichText(
                      text: TextSpan(
                        text: L.thank_you_for_choosing_linksay_app.tr,
                        style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.bold, color: AppColors.colorFFC2AF8C, height: 1.3),
                        children: [
                          const TextSpan(
                            text: "\n \n",
                          ),
                          TextSpan(
                            text: L.user_agreement_and_privacy_policy.tr,
                          ),
                          TextSpan(
                            text: L.user_service_agreement.tr,
                            style: const TextStyle(color: AppColors.colorFFF8EEA1),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                if (Config.isOversea) {
                                  Get.to(
                                    PdfView(
                                      title: L.terms_of_use.tr,
                                      url: Config.termsOfUseUrlIm,
                                    ),
                                  );
                                }
                              },
                          ),
                          TextSpan(text: L.and.tr),
                          TextSpan(
                            text: L.rivacy_clause.tr,
                            style: const TextStyle(color: AppColors.colorFFF8EEA1),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                if (Config.isOversea) {
                                  Get.to(
                                    PdfView(
                                      title: '${L.privacy_policy.tr}\n',
                                      url: Config.privacyPolicyUrlIm,
                                    ),
                                  );
                                }
                              },
                          ),
                          TextSpan(text: L.all_terms.tr),
                          TextSpan(
                            text: "\n",
                          ),
                          TextSpan(text: L.user_agreement_and_privacy_policy_1.tr),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.r),
                    /// logo & 按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        BlendMask(
                          opacity: 1.0,
                          blendMode: BlendMode.overlay,
                          child: Image.asset(R.textLogoTransparent, width: 94.5.r, height: 62.02.r),
                        ),
                        WoodButton(
                          onTap: () {
                            Get.find<AppConfigService>().saveUserAgreement(true);
                            Get.find<SecureStoreService>().storage.deleteAll();
                            // Get.offAllNamed(Routes.ACCOUNT_MAIN);
                            Get.offNamedUntil(Routes.PhonePage, (_) => false);
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 35.r),
                  ],
                ),
              ),
            ],
          ),
        ),
        /// Agreement标题显示
        Center(
          child: Container(
            width: 310.r,
            height: _agreementTitleHeight,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(R.agreementTitleBg),
                fit: BoxFit.fill,
              ),
            ),
            child: Center(
              child: Text(L.user_agreement_and_privacy_policy_title.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.colorFFFFC670,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}