import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/app_log.dart';
import '../../../../../core/utils/jump.dart';
import '../../../../../core/utils/util.dart';
import '../../../../data/models/res/invite/invite_info_model_list_res.dart';
import '../../../../data/models/user_message_model.dart';
import '../../../../data/providers/api/invite.dart';
import '../../../../data/providers/api/own.dart';
import '../../../../data/providers/db/database.dart';
import '../../../../data/services/config_service.dart';

class InviteDetailController extends GetxController {
  final conf = Get.find<AppConfigService>();
  final api = Get.find<InviteApi>();
  late ScrollController scrollController;
  late int curPage;
  late int lastPage;
  late int pageSize;
  late int pageNumber;
  RxList<InviteInfoListDataModel> dataList = RxList();
  var number = 0.obs;

  @override
  void onInit() {
    onRefresh();
    scrollController = ScrollController();
    scrollController.addListener(() {
      AppLogger.d(
          'DaoController  pixels===${scrollController.position.pixels}');
      AppLogger.d(
          'DaoController  maxScrollExtent===${scrollController.position.maxScrollExtent}');
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (curPage <= lastPage) {
          pageNumber++;
          AppLogger.d(
              'DaoController  scrollController _onLoadMore pageNumber==$pageNumber lastpage==$lastPage');
          _onLoadMore();
        }
      }
    });
    super.onInit();
  }

  onRefresh() {
    AppLogger.d("onRefresh!!!");
    curPage = 1;
    lastPage = 1;
    pageSize = 10;
    pageNumber = 1;
    _getInviteInfo();
    _onLoadMore(isRefresh: true);
  }

  _getInviteInfo() async {
    // showLoadingDialog();
    var userNameWithoutDomain = conf.getUserNameWithoutDomain();
    var res = await api.inviteInfo(userNameWithoutDomain);
    dismissLoadingDialog();
    // var inviteUserName = res.data?.data?.userName??"";
    // String readRecommenderUserNameWithDomain = Get.find<AppConfigService>().readRecommenderUserNameWithDomain();
    // if(readRecommenderUserNameWithDomain.isNotEmpty){
    //   inviteUserName=readRecommenderUserNameWithDomain;
    // }
    number.value = res.data?.data?.number ?? 0;
    // status.value = res.data?.data?.status ?? -1;
    // if (inviteUserName.isNotEmpty && inviteUserName != recommender.value.name) {
    //   recommender.value = OwnInfoModelData(
    //     name: inviteUserName,
    //     nickname: inviteUserName.length > 7
    //         ? inviteUserName.substring(0, 7)
    //         : inviteUserName,
    //   );
    // }
  }

  _onLoadMore({bool isRefresh = false}) async {
    var userNameWithoutDomain = conf.getUserNameWithoutDomain();
    var response =
        await api.inviteInfoList(userNameWithoutDomain, pageSize, pageNumber);
    var list = response.data?.data?.list;
    var lastPage = response.data?.data?.lastPage;
    var currentPage = response.data?.data?.currentPage;
    // var total = response.body?.data?.total;
    if (lastPage != null) {
      this.lastPage = lastPage;
    }
    if (currentPage != null) {
      curPage = currentPage;
    }
    if (list != null) {
      for (InviteInfoListDataModel el in list) {
        el.nickname = (el.invitedUserName?.length ?? 0) > 7
            ? el.invitedUserName?.substring(0, 7)
            : el.invitedUserName;
      }
      AppLogger.d("list length==${list.length} dataList.");
      if (isRefresh) {
        dataList.clear();
      }
      if (list.isNotEmpty) {
        _updateUserInfo(list);
        dataList.addAll(list);
      }
    }
    // dataList.addAll([
    //   InviteInfoListDataModel(
    //     nickname: "小李",
    //     invitedUserName: 'AFuhjN6VbGriiZvVyzkN29kt5yYt16zZe7bXDGyJAKCZ@',
    //     createTime: '2024-01-01 12:00:00',
    //   ),
    //   InviteInfoListDataModel(
    //     nickname: "小李",
    //     invitedUserName: 'AFuhjN6VbGriiZvVyzkN29kt5yYt16zZe7bXDGyJAKCZ@',
    //     createTime: '2024-01-01 12:00:00',
    //   ),
    // ]);
  }

  _updateUserInfo(List<InviteInfoListDataModel>? models) async{
    if(models==null){
      return;
    }
    var names = models.map((e) => '${e.invitedUserName}@');
    // List<String> names  = [];//= models.map((e) => '${e.invitedUserName}@');
    // names.add('AFuhjN6VbGriiZvVyzkN29kt5yYt16zZe7bXDGyJAKCZ@');
    if(names.isEmpty){
      return;
    }
    var notNodeUsers = <String>[];
    var list = await getOtherInfoNew(List.from(names)) ?? [];
    for (var e in list) {
      var bean = dataList.firstWhereOrNull((d) =>  e.name?.contains('${d.invitedUserName}')??false);
      if (bean != null) {
        bean.nickname = e.nickname;
        bean.avatar = e.avatar;
        bean.userName = e.name;
        update([bean.invitedUserName??'']);
      }
      if(e.name?.endsWith('@')??false){
        notNodeUsers.add(e.name??'');
      }
    }
    if(notNodeUsers.isNotEmpty){
      // notNodeUsers = ['3LwtmsVeaNBmFqP74CfCPpmYkCzhCm7nqr3kFeMckeec@'];
      Get.find<AppDatabase>().contactInfoByName(notNodeUsers).get().then((value){
        for (var e in value) {
          var bean = dataList.firstWhereOrNull((d) =>  e.username.startsWith('${d.invitedUserName}'));
          if (bean != null) {
            bean.nickname = e.localname??e.displayname;
            bean.avatar = e.avatarUrl;
            bean.userName = e.username;
            update([bean.invitedUserName??'']);
          }
        }
      });
    }
    AppLogger.d('_updateUserInfo list=${list.length}');
  }

  clickItem(InviteInfoListDataModel? data) {
    if (data == null ||
        data.userName == null ||
        !(data.userName!.contains('@')) ||
        (data.userName!.endsWith('@'))) {
      return;
    }

    var userMessage = UserMessage(
        chatType: 0,
        displayName: data.nickname,
        userName: data.userName,
        avatarPath: data.avatar);
    // Get.toNamed(Routes.MESSAGE, arguments: userMessage);
    JumpPage.messgae(userMessage);
  }
}