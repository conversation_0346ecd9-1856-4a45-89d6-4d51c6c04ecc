import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../core/values/colors.dart';
import '../../../../../../r.dart';
import '../../../../../widgets/mask/blend_mask.dart';
import '../../../../../widgets/mavatar_circle_avatar.dart';
import '../../mine_controller.dart';

class InvitationCodeDisplay extends StatelessWidget {
  const InvitationCodeDisplay({super.key, this.onCopyTap, required this.text, this.heightRatio = 1, this.widthRatio = 1, this.labelFontSize, this.inviteCodeFontSize, this.label, this.avatarImagePath, this.avatarText});
  final Function()? onCopyTap;
  final String text;
  final double heightRatio;
  final double widthRatio;
  final String? label;
  final double? labelFontSize;
  final double? inviteCodeFontSize;
  final String? avatarImagePath;
  final String? avatarText;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 115.62.r * heightRatio,
      width: 300.04.r * widthRatio,
      child: Stack(
        children: [

          /// 邀请码部分
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: onCopyTap,
              child: Container(
                width: 239.8.r * widthRatio,
                height: 99.67.r  * heightRatio,
                padding: EdgeInsets.only(top: 33.3.r * heightRatio, bottom: 30.37.r * heightRatio),
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(R.sharePromoteCodeBg),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 44.37.r * widthRatio),
                    
                    /// 邀请码
                    BlendMask(
                      blendMode: BlendMode.hardLight,
                      child: SizedBox(
                        width: (onCopyTap != null ? 97.r : 145.39.r) * widthRatio,
                        child: Text(
                          text,
                          maxLines: 1,
                          textAlign: TextAlign.left,
                          style: TextStyle(
                            fontSize: inviteCodeFontSize ?? 25.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.colorFF3A2107,
                            shadows: [
                              Shadow(
                                blurRadius: 2.r,
                                color: AppColors.colorFF442105,
                              ),
                              Shadow(
                                blurRadius: 2.r,
                                color: AppColors.colorFF553011,
                              ),
                            ]
                          ),
                        ),
                      ),
                    ),
                    if(onCopyTap != null)
                      SizedBox(width: 8.r),
                    if(onCopyTap != null)
                      BlendMask(
                        blendMode: BlendMode.hardLight,
                        child: Image.asset(
                          R.sharePromoteCopy,
                          width: 20.r,
                          height: 20.r,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          /// 用户头像部分
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: 93.21.r * widthRatio,
              height: 115.62.r * heightRatio,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(R.sharePromoteAvatarBg),
                  fit: BoxFit.fill,
                ),
              ),
              child: Center(
                child: Container(
                  width: 46.r * widthRatio,
                  height: 46.r * widthRatio,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: AssetImage(R.sharePromoteAvatarFrame),
                      fit: BoxFit.fill,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.colorFF000000,
                        blurRadius: 3.r,
                        offset: Offset(0, 2.r),
                      ),
                    ],
                  ),
                  child: Center(
                    child:  MAvatarCircle(
                      diameter: 43 * widthRatio,
                      text: avatarText ?? Get.find<MineController>().userLocalName.value,
                      imagePath: avatarImagePath ?? Get.find<MineController>().userIconPath.value,
                    ),
                  ),
                ),
              ),
            ),
          ),

          /// label 部分
          if(label != null)
            Positioned(
              left: 104.6.r * widthRatio,
              top: 10.r * heightRatio,
              child: Text(
                label!,
                style: TextStyle(
                  fontSize: labelFontSize ?? 15.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.colorFF83623E,
                ),
              ),
            ),
        ],
      ),
    );
  }
}