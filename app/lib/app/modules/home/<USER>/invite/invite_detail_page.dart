import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/invite/invite_detail_controller.dart';
import 'package:flutter_metatel/app/widgets/button/wood_button.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../data/models/res/invite/invite_info_model_list_res.dart';
import '../../../../widgets/brown_app_bar.dart';
import '../../../../widgets/mavatar_circle_avatar.dart';
import '../../../../widgets/text_middle_overlow.dart';
import 'share_poster.dart';

class InviteDetailPage extends GetView<InviteDetailController> {
  const InviteDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BrownAppBar(
      title: L.invitation_list.tr,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 10.r),
        decoration: BoxDecoration(
          // color: Colors.white.withOpacity(0.9),
          image: DecorationImage(image: AssetImage(R.sharePromoteBg), fit: BoxFit.cover),
        ),
        child: RefreshIndicator(
          displacement: 5,
          onRefresh: () async {
            controller.onRefresh();
          },
          notificationPredicate: (_) {
            return true;
          },
          child: Column(
            children: [
              SizedBox(height: 17.r),
              Row(
                children: [
                  SizedBox(width: 15.r),
                  Text(
                    L.already_invited.tr + "：",
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.colorFF83623E,
                    ),
                  ),
                  Obx(
                    () => Text(
                      "${controller.number.value}",
                      style: TextStyle(
                        fontSize: 25.sp,
                        fontWeight: FontWeight.w900,
                        color: AppColors.colorFF6D0000,
                      ),
                    ),
                  ),
                  const Spacer(),
                  WoodButton(
                    buttonWidth: 159.r,
                    text: L.invite_now.tr,
                    suffixIcon: R.iconInviteNow,
                    onTap: () => Get.to(() => SharePoster()),
                  ),
                  SizedBox(width: 5.r),
                ],
              ),
              SizedBox(height: 23.33.r),
              Divider(
                height: 0.5,
                thickness: 0.5,
                color: AppColors.colorFF6E5F4E,
              ),
              SizedBox(height: 10.r),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 19.r),
                  child: Obx(
                    () => ScrollConfiguration(
                      behavior: ScrollBehavior().copyWith(overscroll: false),
                      child: ListView.separated(
                          controller: controller.scrollController,
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          physics: const AlwaysScrollableScrollPhysics(),
                          itemCount: controller.dataList.length,
                          itemBuilder: (ctx, index) {
                            var data = controller.dataList[index];
                            return buildItem(
                              data, 
                              onPressed: () {
                                controller.clickItem(data);
                              },
                            );
                          },
                          separatorBuilder: (context, index) {
                            return Divider(
                              height: 0.5,
                              thickness: 0.5,
                              color: AppColors.colorFF6E5F4E,
                            );
                          },
                        ),
                    ),  
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildItem(
    InviteInfoListDataModel model, {
    // double paddingTop = 20,
    // double nickNameSize = 16,
    // Color nickNameColor = AppColors.white,
    // Color userNameColor = AppColors.white,
    // FontWeight fontWeight = FontWeight.w500,
    VoidCallback? onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.only(top: 20.33).r,
        // padding: padding,
        // alignment: Alignment.center,
        child: GetBuilder<InviteDetailController>(
          id: model.invitedUserName,
          builder: (controller) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// 头像、名称、username
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 头像
                    createHeader(model.avatar, model.nickname),
                    // 间隔
                    SizedBox(width: 15.r),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 1.r),
                          
                          /// 名称
                          Text(
                            model.nickname ?? "",
                            style: TextStyle(
                              fontSize: 17.sp,
                              color: AppColors.colorFF1F1B1C,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),

                          /// Username
                          SizedBox(
                            width: 192.r,
                            child: MiddleText(
                              userNameDelDomain(model.invitedUserName) ?? "",
                              WXTextOverflow.ellipsisMiddle,
                              style: TextStyle(
                                color: AppColors.colorFF6E5F4E,
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),

                          // Row(
                          //   children: [
                          //     Text(
                          //       model.createTime ?? "",
                          //       style: TextStyle(
                          //         fontSize: 12.sp,
                          //         color: AppColors.colorFFB9B9B9,
                          //       ),
                          //     ),
                          //   ],
                          // ),
                          // Row(
                          //   children: [
                          //     Expanded(
                          //       child: MiddleText(
                          //         userNameDelDomain(model.invitedUserName) ?? "",
                          //         WXTextOverflow.ellipsisMiddle,
                          //         style: TextStyle(
                          //             color: userNameColor,
                          //             fontSize: 12.sp,
                          //             fontWeight: FontWeight.w500),
                          //       ),
                          //     ),
                          //     SizedBox(
                          //       width: 10.w,
                          //     ),
                          //   ],
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 9.r),

                /// 时间
                Padding(
                  padding: EdgeInsets.only(left: 65.r),
                  child: Text(
                    model.createTime ?? "",
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: AppColors.colorFF6E5F4E,
                    ),
                  ),
                ),
                SizedBox(height: 14.67.r),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget createHeader(String? avatar, String? nickName) {
    return MAvatarCircle(
      diameter: 50.h,
      imagePath: avatar,
      isNet: (avatar != null && avatar.isNotEmpty),
      textStyle: const TextStyle(fontSize: 16, color: Colors.white),
      text: nickName,
    );
  }
}