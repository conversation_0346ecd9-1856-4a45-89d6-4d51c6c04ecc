import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/contact_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/utils/jump.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/channel.dart';
import '../../../../r.dart';
import '../../../widgets/popupmutu_item.dart' as p;
import '../../../../core/languages/l.dart';
import '../../../../core/values/config.dart';
import '../../../../core/values/colors.dart';
import '../../../../routes/pages.dart';

enum Options { createGroup, createPrivateGroup, addFriend, scan, createMeeting, aiTid }

enum SearchType { sessions, contacts, square }

typedef CallBack = Function(int index);

class HomeAppBarBase extends StatelessWidget {
  const HomeAppBarBase({
    Key? key,
    required this.type,
    this.canSearch,
    this.showTitle,
  }) : super(key: key);

  final SearchType type;
  final bool? canSearch;
  final bool? showTitle;

  /// 菜单选中项
  void _onMenuItemSelected(int value) {
    Options options = Options.values[value];
    switch (options) {
      case Options.createGroup:
        Get.toNamed(Routes.CREATE_GROUP, arguments: {'isPrivateGroup': false});
        break;
      case Options.createPrivateGroup:
        Get.toNamed(Routes.CREATE_GROUP, arguments: {'isPrivateGroup': true});
        break;
      case Options.addFriend:
        Get.toNamed(Routes.ADDFRIEND);
        break;
      case Options.scan:
        JumpPage.toScan();
        break;
      case Options.createMeeting:
        // JumpPage.toMeeting();
        Get.toNamed(Routes.MeetingView);
        break;
      case Options.aiTid:
        JumpPage.aiHosting();
        break;
      default:
    }
  }

  Widget buildSearchIcon() {
    return IconButton(
      onPressed: () {
        int searchType = SearchResultType.all;
        if (type == SearchType.sessions) {
          searchType =
              SearchResultType.chat | SearchResultType.function | SearchResultType.group | SearchResultType.contactor;
        } else if (type == SearchType.contacts) {
          searchType = SearchResultType.contactor;
        }
        Get.toNamed(Routes.SEARCH, arguments: {'type': searchType});
      },
      icon: Image.asset(
        R.icoSearch,
        width: 19.r,
        height: 19.r,
      ),
    );
  }

  Widget buildSearch1({
    required double height,
    String? hintText,
    Color? fillColor,
  }) {
    return SizedBox(
      height: height,
      child: TextField(
        onTap: () {
          if (type == SearchType.square) {
            Get.toNamed(Routes.SearchSquare);
          } else {
            int searchType = SearchResultType.all;
            if (type == SearchType.sessions) {
              searchType = SearchResultType.chat |
                  SearchResultType.function |
                  SearchResultType.group |
                  SearchResultType.contactor;
            } else if (type == SearchType.contacts) {
              searchType = SearchResultType.contactor;
            }
            Get.toNamed(Routes.SEARCH, arguments: {'type': searchType});
          }
        },
        readOnly: true,
        textAlign: TextAlign.start,
        textAlignVertical: TextAlignVertical.center,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(color: AppColors.colorFF7F7F7F, fontSize: 12.sp, fontWeight: FontWeight.w400),
          // 设置后，提升文本居中
          contentPadding: EdgeInsets.zero,
          prefixIcon: Icon(
            Icons.search,
            size: 17.r,
            color: Color.fromARGB(255, 89, 90, 90),
          ),
          filled: true,
          fillColor: fillColor,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.all(Radius.circular(25)),
          ),
        ),
      ),
    );
  }

  showMenuView(BuildContext context) {
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    final RenderBox button = context.findRenderObject() as RenderBox;
    final Offset position = button.localToGlobal(
      Offset.zero,
      ancestor: overlay,
    );

    // 显示自定义菜单
    showMenu(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10).r,
        side: BorderSide(color: AppColors.colorFF6E5F4E, width: 1.r),
        // side: BorderSide.none, // 移除边框
      ),
      elevation: 1,
      menuPadding: EdgeInsets.zero,
      color: AppColors.colorFF26190B,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy + button.size.height + 20,
        position.dx + button.size.width,
        position.dy + button.size.height,
      ),
      items: [
        _buildPopupMenuItemNew(
          L.create_group_txt.tr,
          'assets/images/create_group.png',
          Options.createGroup.index,
          (index) {
            _onMenuItemSelected(index);
          },
          // hint: L.up_to_people.trParams(
          //   {'number': Channel.maxMemberLimitChannel},
          // ),
        ),

        // _buildPopupMenuItemNew(
        //   L.create_private_group_txt.tr,
        //   'assets/images/private_group.png',
        //   Options.createPrivateGroup.index,
        //       (index) {
        //     _onMenuItemSelected(index);
        //   },
        //   hint: L.up_to_people.trParams(
        //     {'number': Channel.maxMemberLimitGroup},
        //   ),
        // ),
        if (Config.meetingUrl.isNotEmpty)
          _buildPopupMenuItemNew(
            L.create_meeting.tr,
            'assets/images/create_meeting.png',
            Options.createMeeting.index,
            (index) {
              _onMenuItemSelected(index);
            },
          ),
        // _buildPopupMenuItem(
        //   L.ai_hosting.tr,
        //   'assets/images/ai_tid.png',
        //   Options.aiTid.index,
        // ),
        _buildPopupMenuItemNew(
          L.add_buddy.tr,
          'assets/images/add_friend.png',
          // Options.addFriend.index,
          Options.scan.index,
          (index) {
            _onMenuItemSelected(index);
          },
        ),
        _buildPopupMenuItemNew(
          L.main_scan.tr,
          'assets/images/scan.png',
          Options.scan.index,
          (index) {
            _onMenuItemSelected(index);
          },
          showDiver: false,
        ),
      ],
    );
  }

  Widget buildMenu() {
    return Theme(
        data: ThemeData(
          splashColor: Colors.transparent, // 禁用水波纹效果
          highlightColor: Colors.transparent, // 禁用高亮效果
        ),
        child: p.KYPopupMenuButton(
          icon: Image.asset(
            R.icoDaoAdd,
            width: 19.r,
            height: 19.r,
          ),
          enableFeedback: false,
          color: AppColors.colorFF262B2B,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: BorderSide.none, // 移除边框
          ),
          elevation: 1,
          padding: EdgeInsets.zero,
          // 在按钮下方显示
          offset: const Offset(0, 50),
          itemBuilder: (context) {
            return <p.KYPopupMenuItem>[
              _buildPopupMenuItem(
                L.create_group_txt.tr,
                'assets/images/create_group.png',
                Options.createGroup.index,
                hint: L.up_to_people.trParams(
                  {'number': Channel.maxMemberLimitChannel},
                ),
              ),

              _buildPopupMenuItem(
                L.create_private_group_txt.tr,
                'assets/images/private_group.png',
                Options.createPrivateGroup.index,
                hint: L.up_to_people.trParams(
                  {'number': Channel.maxMemberLimitGroup},
                ),
              ),
              if (Config.meetingUrl.isNotEmpty)
                _buildPopupMenuItem(
                  L.create_meeting.tr,
                  'assets/images/create_meeting.png',
                  Options.createMeeting.index,
                ),
              // _buildPopupMenuItem(
              //   L.ai_hosting.tr,
              //   'assets/images/ai_tid.png',
              //   Options.aiTid.index,
              // ),
              _buildPopupMenuItem(
                L.add_buddy.tr,
                'assets/images/add_friend.png',
                Options.addFriend.index,
              ),
              _buildPopupMenuItem(L.main_scan.tr, 'assets/images/scan.png', Options.scan.index, showDiver: false),
            ];
          },
          onSelected: (value) {
            _onMenuItemSelected(value as int);
          },
        ));
  }

  Widget buildMenuNew(BuildContext context) {
    return Theme(
        data: ThemeData(
          splashColor: Colors.transparent, // 禁用水波纹效果
          highlightColor: Colors.transparent, // 禁用高亮效果
        ),
        child: Builder(builder: (context) {
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: Container(
              width: 44.r,
              height: 52.r,
              // padding: EdgeInsets.only(left: 11.r, right: 11.r, top: 15.r, bottom: 15.r),
              child: Center(
                child: Image.asset(
                  R.icoDaoAdd,
                  width: 22.r,
                  height: 22.r,
                  color: AppColors.colorFFCAB692,
                ),
              ),
            ),
            onTap: () {
              showMenuView(context);
              // _onMenuItemSelected(value as int);
            },
          );
        }));
  }

  Widget buildContact() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Get.to(const ContactView());
      },
      child: Container(
        width: 44.r,
        height: 52.r,
        // padding: const EdgeInsets.only(left: 11, top: 11, bottom: 15, right: 15).r,
        child: Center(
          child: Image.asset(
            R.iconSearchRightContact,
            width: 21.7.r,
            height: 21.7.r,
            color: AppColors.colorFFCAB692,
          ),
        ),
      ),
    );
  }

  Widget buildSearch() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (type == SearchType.square) {
          Get.toNamed(Routes.SearchSquare);
        } else {
          int searchType = SearchResultType.all;
          if (type == SearchType.sessions) {
            searchType =
                SearchResultType.chat | SearchResultType.function | SearchResultType.group | SearchResultType.contactor;
          } else if (type == SearchType.contacts) {
            searchType = SearchResultType.contactor;
          }
          Get.toNamed(Routes.SEARCH, arguments: {'type': searchType});
        }
      },
      child: Container(
        width: 44.r,
        height: 52.r,
        // padding: const EdgeInsets.only(left: 11, top: 11, bottom: 15, right: 15).r,
        child: Center(
          child: Image.asset(
            R.ivSearch,
            width: 20.r,
            height: 20.r,
            color: AppColors.colorFFCAB692,
          ),
        ),
      ),
    );
  }

  /// 构建弹出菜单Item
  p.KYPopupMenuItem _buildPopupMenuItem(String title, String imageName, int position,
      {String? hint, bool showDiver = true}) {
    return p.KYPopupMenuItem(
      value: position,
      padding: EdgeInsets.only(left: 1.r),
      height: 41.r,
      child: Column(
        children: [
          Container(
            height: 39.r,
            padding: EdgeInsets.only(left: 13.r),
            child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
              Image.asset(imageName, width: 15.r, height: 15.r),
              SizedBox(width: 8.w),
              Padding(
                padding: const EdgeInsets.only(right: 20).r,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w400, color: AppColors.white),
                    ),
                    if (hint?.isNotEmpty ?? false)
                      Text(
                        hint ?? '',
                        style: TextStyle(fontSize: 8.sp, color: AppColors.colorFF7F7F7F),
                      ),
                  ],
                ),
              ),
            ]),
          ),
          if (showDiver) Divider(height: 0.5, indent: 0, color: AppColors.colorDivider)
        ],
      ),
    );
  }

  PopupMenuItem _buildPopupMenuItemNew(String title, String imageName, int position, CallBack callBack,
      {String? hint, bool showDiver = true}) {
    return PopupMenuItem(
      value: position,
      onTap: () {
        callBack.call(position);
      },
      // padding: EdgeInsets.only(left: 1.r),
      padding: EdgeInsets.zero,
      height: 43.r,
      child: Column(
        children: [
          Container(
            height: 42.5.r,
            // padding: EdgeInsets.only(left: 13.r),
            child: Row(
              children: [
                SizedBox(width: 16.r),
                Image.asset(
                  imageName,
                  width: 14.r,
                  height: 14.r,
                  color: AppColors.colorFFC2AF8C,
                ),
                SizedBox(width: 17.r),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        height: 1.1,
                        fontSize: hint?.isNotEmpty ?? false ? 13.sp : 15.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.colorFFC2AF8C,
                      ),
                    ),
                    if (hint?.isNotEmpty ?? false) SizedBox(height: 4.r),
                    if (hint?.isNotEmpty ?? false)
                      Text(
                        hint ?? '',
                        style: TextStyle(
                          height: 1.1,
                          fontSize: 8.sp,
                          color: AppColors.colorFF7F7F7F,
                        ),
                      ),
                  ],
                ),
                SizedBox(width: 65.r),
              ],
            ),
          ),
          if (showDiver)
            Divider(height: 0.5.r, thickness: 0.5, indent: 0, color: AppColors.colorFFC2AF8C.withOpacity(0.2))
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
