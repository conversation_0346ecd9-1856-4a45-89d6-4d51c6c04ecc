import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/avatar_model.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/realname/real_name_page_controller.dart';
import 'package:flutter_metatel/app/modules/message/components/attachment_dialog.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/screen.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

import '../../../../../core/values/colors.dart';

class RealNamePage extends StatefulWidget {
  const RealNamePage({super.key});

  @override
  State<StatefulWidget> createState() => _SetMySelfInfoPage();
}

class _SetMySelfInfoPage extends State<RealNamePage> {
  RealNameController controller = Get.put(RealNameController());

  @override
  void dispose() {
    Get.delete<RealNameController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundGray,
      appBar: AppBarCommon().build(
        context,
        title: L.real_authenticate.tr,
        backgroundColor: AppColors.backgroundGray,
      ),
      body: Container(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            TextButton(
                onPressed: () {
                  _delete();
                },
                child: Text('删除小程序')),
            TextButton(onPressed: () {
              _addUniApp();
            }, child: Text('添加小程序')),
            TextButton(onPressed: () {
              openUniapp();
            }, child: Text('打开小程序（__UNI__609C541）')),
            TextButton(onPressed: () {
              sendUserInfo();
            }, child: Text('发送用户信息')),
            TextButton(onPressed: () async{
              var res = await Screen.closeMP(appid: '__UNI__609C541');
              toast(res==true ?'关闭成功':'关闭失败');
            }, child: Text('关闭小程序')),
            TextButton(onPressed: () async{
              var res = await getVersion();
              toast('版本号：$res');
            }, child: Text('获取小程序版本')),
          ],
        ),
      ),
    );
  }

  _delete() {
    var path = getUniAppParentDirPath();
    var file = Directory(path);
    if (file.existsSync()) {
      file.deleteSync(recursive: true);
      toast('删除成功');
    }
  }

  _addUniApp() async {
    var result = await FilePicker.platform.pickFiles(allowMultiple: true);
    if (result == null) {
      return;
    }
    List<String?> filePaths = result.paths.map((path) => path).toList();
    if (filePaths.length > 1) {
      toast('只能选择一个');
      return;
    }
    var filePath = filePaths.first;
    if (!filePath!.endsWith('.wgt')) {
      toast('文件格式不正确');
      return;
    }
    var p = filePath.split('/');
    var appid = p.last;
    appid = appid.replaceAll('.wgt', '');
    AppLogger.d('_addUniApp appid=$appid');
    Screen.installMP(appid: appid, wgtPath: filePath);
  }

  openUniapp() async{
    //初始化小程序
    var res=  await Screen.init(receive: (event) async {
      //文件选择
      if(event['event'] == 'choose_file') {
        Screen.hideMP(appid: event['appid']);
        Map data = event['data'];

        // TODO编写的文件选择功能
        List files = [];

        Screen.callbackMP(appid: event['appid'], event: event['event'],data: {"files": files});
        Screen.openMP(appid: event['appid']);
      }
      if(event['event']=='getUserInfo') {
        sendUserInfo();
        // Screen.callbackMP(appid: event['appid'], event: 'apptoUniapp',data: {"data": 11111,"value":3333333});
      }
      AppLogger.d('loadTest init event =${event['event']}');

    });
    AppLogger.d('loadTest init res=$res');

    res = await Screen.openMP(appid: '__UNI__609C541');
    AppLogger.d('loadTest openMP res=$res');
    res = await Screen.versionMP(appid:'__UNI__609C541');
    AppLogger.d('loadTest versionMP res=$res');
  }

  sendUserInfo() {
    Screen.callbackMP(appid: '__UNI__609C541', event: 'userInfo',data: getUserInfo());
  }

  Future<String?> getVersion() async{
  var   res = await Screen.versionMP(appid:'__UNI__609C541');
    AppLogger.d('loadTest versionMP res=$res');
    return res;
  }

  Map<String,dynamic> getUserInfo() {
    Map<String,dynamic> map = {};
    AvatarModel avatar = AvatarModel.fromJson(
        Get.find<AppConfigService>().getMySelfAvatarInfo() ?? {});
    map['user_name'] = Get.find<AppConfigService>().getUserName();
    map['nick_name'] = Get.find<AppConfigService>().getMySelfDisplayName();
    map['icon'] = avatar.avatarUrl;
    map['language'] = currentLanguageIsSimpleChinese() ? 'zh' : 'en';
    AppLogger.d('getUserInfo info=$map');
    return map;
  }

}
