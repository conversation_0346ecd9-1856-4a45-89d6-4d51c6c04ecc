import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/api/register_api.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/events_bus.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/config.dart';
import '../../../../data/events/events.dart';
import '../../../../data/providers/api/own.dart';
import '../../../../data/services/config_service.dart';

class RealNameController extends GetxController {
  var userIconPath = "".obs;
  var userLocalName = "".obs;
  RxInt realType = 0.obs;
  RxInt count = 3.obs;//可验证次数
  final FocusNode focusNode = FocusNode();
  final List<StreamSubscription> _subscriptions = [];
  TextEditingController phoneEditController = TextEditingController();
  final TextEditingController userNameController = TextEditingController();
  final TextEditingController idcardController = TextEditingController();
  String? phoneNumber;
  bool readAuthenticationStatus = false;


  @override
  void onClose() {
    focusNode.removeListener(focusListener);
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    phoneEditController.dispose();
    userNameController.dispose();
    idcardController.dispose();
    canReal();
    super.onClose();
  }

  focusListener() {
    if (focusNode.hasFocus) {
      if (phoneEditController.text.isEmpty) {
        phoneEditController.text = userLocalName.value;
        update();
      }
    }
  }

  @override
  void onInit() async {
    super.onInit();
    phoneNumber = Get.find<AppConfigService>().getPhone();
    readAuthenticationStatus = Get.find<AppConfigService>().readAuthenticationStatus();
    if (readAuthenticationStatus) {
      realType.value = 2;
    }
    phoneEditController.text = phoneNumber ?? '';

  }



  void updateMyself(String disPlayName, String fistName, String lastName) {
    submitOwnInfo(nickName: disPlayName).then((value) {
      if (value) {
        AppConfigService conf = Get.find<AppConfigService>();
        conf.saveMySelfDisplayName(disPlayName);
        conf.saveMyselfFirstName(fistName);
        conf.saveMyselfLastName(lastName);
        Get.find<EventBus>().fire(ContactDataMyselfUpdateEvent());
        userLocalName.value = disPlayName;
        Get.find<EventBus>()
            .fire(MiningTaskEvent(MiningTaskType.MINING_TASK_UPDATE_NICKNAME));
        Get.back();
      } else {
        toast(L.fail_to_edit.tr);
      }
    });
  }

  submit(int type) async {
    if (type == 2) {
      var cardid = idcardController.text.trim();
      var userName = userNameController.text.trim();
      var phone = phoneNumber ?? phoneEditController.text;

      if (phone.isEmpty) {
        phone = phoneEditController.text;
      }
      if (cardid.isEmpty) {
        toast(L.idcard_not_empty.tr);
        return;
      } else if (userName.isEmpty) {
        toast(L.qr_code_name_not_empty.tr);
        return;
      } else if (phone.isEmpty) {
        toast(L.phone_not_empty.tr);
        return;
      }
      if (count.value <= 0) {
        toast(L
            .today_number_of_times_hasbeen_used_up_pleasetry_again_tomorrow.tr);
        return;
      }

      var res = await Get.find<RegisterApiProvider>()
          .realNameAuth(phone, userName, cardid);
      if (res.statusCode == 200) {
        saveTimes();
      }
      if (res.data != null && (res.data['code'] == 200) || res.data['code'] == 111) {
        realType.value = 2;
        Get.find<AppConfigService>().saveAuthenticationStatus(true);
        Get.find<MineController>().updateAuthenticationStatus();
      } else {
        toast('${L.real_authenticate_error.tr} ${res.data?['msg']}');
      }
    } else {
      realType.value = type;
    }
  }

  canReal() {
    var data = TimeTask.instance.getNowDateTime();
    var key =  '${data.month}-${data.day}';
    var config = Get.find<AppConfigService>();
    var counts = config.readAuthenticationTimes();
    AppLogger.d('counts = $counts');
    if(counts==null) {
      count.value = 3;
    } else {
      var t = counts.split('-');
      if(t.isNotEmpty&&t.length==3) {
        var vt = '${t[0]}-${t[1]}';
        if(vt == key) {
          count.value = int.parse(t.last);
        } else {
          count.value = 3;
        }
      }
    }
  }

  saveTimes() {
    if (count.value >= 1) {
      count.value = count.value - 1;
      var data = TimeTask.instance.getNowDateTime();
      var key = '${data.month}-${data.day}-${count.value}';
      var config = Get.find<AppConfigService>();
      AppLogger.d('saveTimes key = $key');
      config.saveAuthenticationTimes(key);
    }
  }
}
