import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/realname/real_name_page_controller.dart';
// import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/brown_app_bar.dart';
import 'package:flutter_metatel/app/widgets/button/wood_button.dart';
import 'package:flutter_metatel/app/widgets/wooden_long_board.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
// import 'package:get/get_connect/http/src/utils/utils.dart';

import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../widgets/brown_text_field.dart';
// import '../../../../widgets/gradient_button.dart';

class RealNamePage extends StatefulWidget {
  const RealNamePage({super.key});

  @override
  State<StatefulWidget> createState() => _SetMySelfInfoPage();
}

class _SetMySelfInfoPage extends State<RealNamePage> {
  RealNameController controller = Get.put(RealNameController());

  @override
  void dispose() {
    Get.delete<RealNameController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BrownAppBar(
      title: L.real_authenticate.tr,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          image: DecorationImage(image: AssetImage(R.settingBg), fit: BoxFit.cover),
        ),
        child: ScrollConfiguration(
          behavior: ScrollBehavior().copyWith(overscroll: false),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 29.r),
              child: Obx(
                () {
                  return controller.realType.value == 0
                      ? _create1View()
                      : controller.realType.value == 1
                          ? _create2View()
                          : _create3View();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 简介板 & 进度条部分
  Widget _createTopView() {
    return Obx(() {
      var type = controller.realType.value;
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          
          /// 简介板
          WoodenLongBoard(text: L.want_to_unlock_more_exciting_things.tr),
          SizedBox(height: 49.47.r),
          
          /// 进度条
          SizedBox(
            width: 305.r,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 19.r,
                  height: 19.r,
                  child: Center(
                    child: Image.asset(
                      R.realNameAuthDone,
                      width: 19.r,
                      height: 19.r,
                    ),
                  ),
                ),
                SizedBox(width: 7.5.r),
                SizedBox(
                  width: 100.r,
                  child: Row(
                    children: [
                      Flexible(
                        child: Container(
                          height: 2,
                          color: AppColors.colorFF83623E,
                        ),
                      ),
                      Flexible(
                        child: Container(
                          height: 2,
                          color: type > 0 ? AppColors.colorFF83623E : AppColors.colorFFCAB692,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 7.5.r),
                SizedBox(
                  width: 19.r,
                  height: 19.r,
                  child: Center(
                    child: Image.asset(
                      controller.realType.value == 0 ? R.realNameAuthPending : R.realNameAuthDone,
                      width: controller.realType.value == 0 ? 12.17.r : 19.r,
                      height: controller.realType.value == 0 ? 12.17.r : 19.r,
                    ),
                  ),
                ),
                SizedBox(width: 7.5.r),
                SizedBox(
                  width: 100.r,
                  child: Row(
                    children: [
                      Flexible(
                        child: Container(
                          height: 2,
                          color: type > 0 ? AppColors.colorFF83623E : AppColors.colorFFCAB692,
                        ),
                      ),
                      Flexible(
                        child: Container(
                          height: 2,
                          color: type > 1 ? AppColors.colorFF83623E : AppColors.colorFFCAB692,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 7.5.r),
                SizedBox(
                  width: 19.r,
                  height: 19.r,
                  child: Center(
                    child: Image.asset(
                      controller.realType.value == 2 ? R.realNameAuthDone : R.realNameAuthPending,
                      width: controller.realType.value == 2 ? 19.r : 12.17.r,
                      height: controller.realType.value == 2 ? 19.r : 12.17.r,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 5.r),
          
          /// 进度条label
          SizedBox(
            width: 335.r,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 60.r,
                  child: Center(
                    child: Text(
                      L.real_authenticate.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.colorFF6E5F4E,
                      ),
                    ),
                  ),
                ),
                const Spacer(),
                SizedBox(
                  width: 60.r,
                  child: Center(
                    child: Text(
                      L.certification_in_progress.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: controller.realType.value == 0 ? FontWeight.w500 : FontWeight.bold,
                        color: AppColors.colorFF6E5F4E,
                      ),
                    ),
                  ),
                ),
                const Spacer(),
                SizedBox(
                  width: 60.r,
                  child: Center(
                    child: Text(
                      L.certification_in_end.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: controller.realType.value == 2 ? FontWeight.bold : FontWeight.w500,
                        color: AppColors.colorFF6E5F4E,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  Widget _create1View() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(height: 11.r),
        _createTopView(),
        SizedBox(height: 35.r),
        Text(
          L.you_have_not_yet_verified_your_name.tr,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.colorFF6E5F4E,
          ),
        ),
        SizedBox(height: 51.r),
        WoodButton(
          buttonWidth: 181.r,
          text: L.start_real_name_authentication.tr,
          onTap: () => controller.submit(1),
        ),
        SizedBox(height: 30.r),
      ],
    );
  }

  Widget _create2View() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(height: 11.r),
        _createTopView(),
        SizedBox(height: 32.r),
        SizedBox(
          width: 300.r,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              
              /// 手机号输入框
              BrownTextField(
                label: L.phone_number.tr,
                hintText: L.phone_number.tr,
                maxLength: 11,
                controller: controller.phoneEditController,
                textAlignVertical: TextAlignVertical.center,
                keyboardType: TextInputType.number,
                readOnly: true,
                prefixIcon: Container(
                  width: 85.r,
                  child: Row(
                    children: [
                      SizedBox(width: 15.r),
                      Text(
                        '+86 ',
                        style: TextStyle(
                          fontSize: 17.sp, 
                          color: AppColors.colorFF6E5F4E,
                          fontWeight: FontWeight.w500,
                          // fontFamily: Config.secondFontFamily,
                        ),
                      ),
                      Icon(
                        Icons.expand_more,
                        size: 15.r,
                        color: AppColors.colorFF6E5F4E,
                      ),
                      // SizedBox(width: 6.r),
                      VerticalDivider(
                        indent: 8.r,
                        endIndent: 8.r,
                        color: AppColors.colorFFE6E1DD,
                        thickness: 1.5,
                      ),
                      // Text(
                      //   '| ',
                      //   textAlign: TextAlign.center,
                      //   style: TextStyle(fontSize: 20.sp,color: AppColors.colorFFE6E1DD),
                      // ),
                    ],
                  ),
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                ],
              ),
              SizedBox(height: 25.r),

              /// 姓名输入框
              BrownTextField(
                label: L.idcard_name.tr,
                hintText: L.idcard_name.tr,
                maxLength: 40,
                controller: controller.userNameController,
                textAlignVertical: TextAlignVertical.center,
              ),
              SizedBox(height: 25.r),

              /// 身份证号输入框
              BrownTextField(
                label: L.idcard.tr,
                hintText: L.idcard.tr,
                maxLength: 20,
                controller: controller.idcardController,
                textAlignVertical: TextAlignVertical.center,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: 50.r),
        WoodButton(
          onTap: () => controller.submit(2),
          text: L.start_certification.tr,
        ),
        SizedBox(height: 7.r,),
        Text(
          L.today_remaining_times.trParams({'num':controller.count.value.toString()}),
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.colorFF6E5F4E,
          ),
        ),
        SizedBox(height: 30.r),
      ],
    );
  }

  Widget _create3View() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(height: 11.r),
        _createTopView(),
        SizedBox(height: 35.r),
        Text(
          L.real_authenticate_success.tr,
          style: TextStyle(fontSize: 15.sp, color: AppColors.colorFF6E5F4E, fontWeight: FontWeight.w500),
        ),
        SizedBox(height: 25.r),
        WoodButton(
          onTap: () => Get.back(),
        ),
        SizedBox(height: 30.r),
      ],
    );
  }
}
