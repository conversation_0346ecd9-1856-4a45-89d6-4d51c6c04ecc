import 'package:cached_network_image/cached_network_image.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../widgets/brown_app_bar.dart';
import '../../message/channelMoment/components/video_thumbnail_widget.dart';
import '../../message/channelMoment/photo_preview_view.dart';
import '../../message/channelMoment/video_player_view.dart';
import 'game_center_detail_controller.dart';

class GameCenterDetailView extends GetView<GameCenterDetailController> {
  const GameCenterDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    var _article = controller.article;
    return BrownAppBar(
      title: L.article_detail.tr,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          image: DecorationImage(image: AssetImage(R.settingBg), fit: BoxFit.cover),
        ),
        child: ScrollConfiguration(
          behavior: ScrollBehavior().copyWith(overscroll: false),
          child: SingleChildScrollView(
            child: Center(
              child: SizedBox(
                width: 400.r,
                child: Column(
                  children: [
                    SizedBox(height: 11.r),

                    /// Banner图片
                    if (_article?.imageUrls != null && _article!.imageUrls!.isNotEmpty)
                      Container(
                        width: 378.r,
                        height: 208.r,
                        decoration: BoxDecoration(
                          image: DecorationImage(image: AssetImage(R.gcArticleDetailBanner), fit: BoxFit.fill),
                        ),
                        child: Center(
                          child: SizedBox(
                            width: 366.r,
                            height: 192.r,
                            child: CachedNetworkImage(
                              imageUrl: (_article.imageUrls != null && _article.imageUrls!.isNotEmpty)
                                  ? _article.imageUrls![0]
                                  : _article.videoThumbnail!,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),

                    if (controller.article?.imageUrls != null && controller.article!.imageUrls!.isNotEmpty)
                      SizedBox(height: 8.r),

                    /// 标题
                    SizedBox(
                      width: 377.r,
                      child: Text(
                        _article!.title ?? '',
                        textAlign: TextAlign.start,
                        maxLines: 2,
                        style: TextStyle(
                          fontSize: 17.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.colorFF6E5F4E,
                        ),
                      ),
                    ),
                    SizedBox(height: 7.09.r),

                    /// 发布时间
                    SizedBox(
                      width: 377.r,
                      child: Text(
                        L.post_date.trParams({'date': _article.createdAt ?? ''}),
                        textAlign: TextAlign.start,
                        maxLines: 1,
                        style: TextStyle(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.colorFF83623E,
                        ),
                      ),
                    ),
                    SizedBox(height: 19.09.r),

                    /// 内容显示
                    Container(
                      width: 400.r,
                      padding: EdgeInsets.symmetric(horizontal: 29.r, vertical: 26.r),
                      decoration: BoxDecoration(
                        image: DecorationImage(image: AssetImage(R.gcArticleDetailContentBg), fit: BoxFit.fill),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (_article.content != null && _article.content!.isNotEmpty)
                            Text(
                              _article.content ?? '',
                              textAlign: TextAlign.start,
                              style: TextStyle(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w500,
                                color: AppColors.colorFF83623E,
                              ),
                            ),
                          // if (_article.content != null &&
                          //     _article.content!.isNotEmpty &&
                          //     ((_article.imageUrls != null && _article.imageUrls!.isNotEmpty) ||
                          //         (_article.videoUrl != null && _article.videoUrl!.isNotEmpty)))
                          //   SizedBox(height: 15.r),

                          if (_article.imageUrls != null && _article.imageUrls!.isNotEmpty)
                            ..._buildImagesSection(_article.imageUrls),

                          if (_article.videoUrl != null && _article.videoUrl!.isNotEmpty)
                            _buildVideoSection(
                              thumbnailUrl: _article.videoThumbnail,
                              onVideoTap: () {
                                Get.to(() => VideoPlayerView(url: _article.videoUrl));
                              },
                            ),
                        ],
                      ),
                    ),
                    SizedBox(height: 5.r),

                    /// 点赞
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            controller.onToggleLike();
                          },
                          child: Obx(
                            () => Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Image.asset(
                                  controller.article!.likeStatus.value ? R.gcArticleLikeActive : R.gcArticleLike,
                                  width: 17.56.r,
                                  height: 17.03.r,
                                ),
                                SizedBox(width: 6.72.r),
                                Text(
                                  controller.article!.likeCount.value.toString(),
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.colorFF83623E,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        // SizedBox(width: 33.r),
                        // GestureDetector(
                        //   behavior: HitTestBehavior.opaque,
                        //   onTap: () {
                        //     if (controller.article == null) return;
                        //     controller.article!.likeStatus == 2
                        //         ? controller.article!.likeStatus = 3
                        //         : controller.article!.likeStatus = 2;
                        //     controller.update();
                        //   },
                        //   child: Row(
                        //     mainAxisSize: MainAxisSize.min,
                        //     children: [
                        //       Image.asset(
                        //         controller.article?.likeStatus == 2 ? R.gcArticleDislikeActive : R.gcArticleDislike,
                        //         width: 17.56.r,
                        //         height: 17.03.r,
                        //       ),
                        //       SizedBox(width: 6.72.r),
                        //       Text(
                        //         (controller.article?.dislikeCount ?? 0).toString(),
                        //         style: TextStyle(
                        //           fontSize: 13.sp,
                        //           fontWeight: FontWeight.w500,
                        //           color: AppColors.colorFF83623E,
                        //         ),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                        SizedBox(width: 12.r),
                      ],
                    ),
                    SizedBox(height: 20.r),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildImagesSection(List<String>? images) {
    // Images(max 9) or Video
    return [
      SizedBox(height: 15.r),
      GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          // images!.length > 4 || images.length == 3
          //     ? 3
          //     : images.length > 1
          //         ? 2
          //         : 1,
          childAspectRatio: 1,
          mainAxisSpacing: 2.r,
          crossAxisSpacing: 3.1.r,
        ),
        itemCount: images!.length,
        itemBuilder: (context, index) {
          return Hero(
            tag: "image_gallery_article_$index",
            child: GestureDetector(
              onTap: () {
                _onImageTap(images, index);
              },
              child: Container(
                padding: EdgeInsets.all(4.r),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.r),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.colorFF83623E,
                      AppColors.colorFFCB9D6A,
                      AppColors.colorFFBE9363,
                      AppColors.colorFF42311F,
                    ],
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6.r),
                  child: ExtendedImage.network(
                    images[index],
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    ];
  }

  void _onImageTap(List<String> images, int selectedIndex) {
    List<GalleryItem> _galleryItems = [];
    if (images.isNotEmpty) {
      for (var i = 0; i < images.length; i++) {
        _galleryItems.add(GalleryItem(heroTag: "image_gallery_article_$i", imageUrl: images[i]));
      }
    }
    Get.to(
      () => PhotoPreviewView(galleryItems: _galleryItems, selectedIndex: selectedIndex),
    );
  }

  Widget _buildVideoSection({
    String? thumbnailUrl,
    Function()? onVideoTap,
  }) {
    return thumbnailUrl == null
        ? SizedBox.shrink()
        : Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 15.r),
              GestureDetector(
                onTap: onVideoTap,
                child: VideoThumbnailWidget(
                  thumbnailUrl: thumbnailUrl,
                  isRoundedCorner: false,
                ),
              ),
            ],
          );
  }
}
