import 'package:get/get.dart';

import '../game_center/game_center_controller.dart';

class GameCenterDetailController extends GetxController {
  GameCenterController _gameCenterController = Get.find<GameCenterController>();
  Article? article;
  int? gameIndex;
  int? articleIndex;

  @override
  void onInit() {
    super.onInit();
    gameIndex = Get.arguments['game_index'];
    articleIndex = Get.arguments['article_index'];
    article = _gameCenterController.getArticle(gameIndex!, articleIndex!);
  }

  void onToggleLike() {
    _gameCenterController.toggleLike(gameIndex!, articleIndex!);
  }
}
