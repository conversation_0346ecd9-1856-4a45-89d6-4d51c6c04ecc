import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/game_center/game_center/game_center_controller.dart';
import 'package:flutter_metatel/app/modules/square/bean/square_banner_bean.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/jump.dart';
import '../../../../r.dart';
import '../../../../routes/pages.dart';
import '../../../data/enums/enum.dart';
import '../../../data/models/user_message_model.dart';
import '../../../widgets/button/wood_button.dart';
import '../../../widgets/mask/blend_mask.dart';
import '../../../widgets/scroll_style_dialog.dart';
import '../../meeting/widgets/create_meeting_btn_border.dart';
import '../../message/channelMoment/components/post_widget.dart';
import '../widgets/payment_dialog.dart';

class GameCenterView extends GetView<GameCenterController> {
  const GameCenterView({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  Widget _buildBody() {
    return Scaffold(
      backgroundColor: AppColors.backgroundGray,
      body: SafeArea(
        bottom: false,
        child:
            // RefreshIndicator(
            // displacement: 5,
            // onRefresh: () async {
            // await controller.refreshData();
            // },
            // notificationPredicate: (notification) {
            //   /// 到滑动到顶部才允许控制scroll notification
            //   /// 并触发刷新相关动画
            //   if (controller.mainScrollController.position.pixels == 0) {
            //     return true;
            //   }
            //   return false;
            // },
            // child:
            Container(
          width: 1.sw,
          height: 1.sh,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            image: DecorationImage(image: AssetImage(R.settingBg), fit: BoxFit.cover),
          ),
          child: Stack(
            children: [
              /// 内容页
              Align(
                alignment: Alignment.centerRight,
                child: _buildContentSection(),
              ),

              /// SideBar
              Align(
                alignment: Alignment.centerLeft,
                child: _buildSideBar(),
              ),

              /// Floating Action Bar
              Visibility(
                visible: true,
                child: Positioned(
                  right: 0.r,
                  bottom: 100.r,
                  child: _buildCreatePostBtn(
                    onTap: controller.onCreatePostTap,
                  ),
                ),
              ),
            ],
          ),
        ),
        // ),
      ),
    );
  }

  Widget _buildSideBar() {
    return Container(
      width: 105.31.r,
      height: 1.sh,
      child: Stack(
        children: [
          /// Sidebar主体
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: 90.16.r,
              height: 1.sh,
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage(R.gcSidebarBg1), fit: BoxFit.fill),
              ),
              child: Stack(
                children: [
                  /// 游戏列表
                  Positioned(
                    top: 90.r,
                    left: 0,
                    right: 0,
                    child: SizedBox(
                      height: 1.sh - 90.r - 131.34.r,
                      child: controller.gameInfos.isEmpty
                          ? const SizedBox.shrink()
                          : ReorderableListView.builder(
                              physics: const BouncingScrollPhysics(),
                              onReorder: (oldIndex, newIndex) {
                                controller.reorderGame(oldIndex, newIndex);
                              },
                              padding: EdgeInsets.zero,
                              // shrinkWrap: true,
                              itemCount: controller.gameInfos.length,

                              itemBuilder: (context, index) {
                                var gameInfo = controller.gameInfos[index];

                                return Container(
                                  color: AppColors.colorFFBD906F,
                                  key: ValueKey(gameInfo.id),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (index == 0) SizedBox(height: 21.r),
                                      if (index != 0) SizedBox(height: 12.5.r),
                                      Obx(
                                        () => _buildGameItem(
                                          gameInfo: gameInfo,
                                          isSelected: index == controller.selectedGameIndex.value,
                                          onTap: () {
                                            controller.selectedGameIndex.value = index;
                                          },
                                        ),
                                      ),
                                      // SizedBox(height: index == controller.gameInfos.length - 1 ? 156.5.r : 25.16.r),
                                      SizedBox(height: 12.5.r),
                                    ],
                                  ),
                                );
                              },
                            ),
                    ),
                  ),

                  /// Sidebar头部分
                  Align(
                    alignment: Alignment.topCenter,
                    child: Container(
                      width: 90.16.r,
                      height: 100.49.r,
                      decoration: BoxDecoration(
                        image: DecorationImage(image: AssetImage(R.gcSidebarBgHead), fit: BoxFit.fill),
                      ),
                      child: Center(
                        child: Image.asset(
                          R.gcMemberNonMBadge,
                          width: 34.r,
                          height: 39.26.r,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          /// Sidebar背景底部卷走
          Positioned(
            bottom: 61.17.r,
            left: 15.15.r,
            child: Image.asset(
              R.gcSidebarBg2,
              width: 100.03.r,
              height: 55.33.r,
              fit: BoxFit.fill,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameItem({required GameInfo gameInfo, bool isSelected = false, Function()? onTap}) {
    double _width = isSelected ? 69.5.r : 57.84.r;
    double _height = isSelected ? 69.5.r : 57.84.r;
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: _width,
        height: _height,
        child: Stack(
          children: [
            Center(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5.r),
                child: gameInfo.image == null
                    ? Container(
                        width: 57.84.r,
                        height: 57.84.r,
                        color: AppColors.colorFF1F1C1C,
                        child: Center(
                          child: Image.asset(
                            R.gcJoinSectionAvatar2,
                            width: 28.35.r,
                            height: 32.34.r,
                          ),
                        ),
                      )
                    : Image.network(
                        gameInfo.image!,
                        width: 57.84.r,
                        height: 57.84.r,
                        fit: BoxFit.cover,
                      ),
              ),
            ),
            Image.asset(isSelected ? R.gcGameFrameActive : R.gcGameFrame, width: _width, height: _height),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSection() {
    return Container(
      width: 1.sw - 90.16.r,
      height: 1.sh,
      child: ScrollConfiguration(
        behavior: ScrollBehavior().copyWith(overscroll: false),
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 38.r),
              _buildBanner(),
              _buildMeetingBtn(),
              _buildInfoSection(),
              _buildArticleSection(),
              SizedBox(height: 100.r),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBanner() {
    return Obx(
      () {
        if (controller.gameInfos.isEmpty) return const SizedBox.shrink();
        RxList<SquareBannerBean> banners = controller.gameInfos[controller.selectedGameIndex.value].banners.obs;
        return Container(
          width: 300.r,
          height: 404.67.r,
          decoration: BoxDecoration(
            image: DecorationImage(image: AssetImage(R.gcBannerFrame1), fit: BoxFit.fill),
          ),
          child: Center(
            child: Container(
              width: 238.r,
              height: 326.76.r,
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage(R.gcBannerFrame2), fit: BoxFit.fill),
              ),
              child: Center(
                child: SizedBox(
                  width: 230.r,
                  height: 319.r,
                  child: banners.isEmpty
                      ? Container(
                          color: AppColors.colorFFFCEAD4,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                R.gcArticleImageEmpty,
                                width: 22.r,
                                height: 22.r,
                              ),
                              SizedBox(width: 5.r),
                              Text(
                                L.image_2.tr,
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.colorFFC2AF8C,
                                ),
                              ),
                            ],
                          ),
                        )
                      : _BannerView(
                          key: ValueKey(controller.selectedGameIndex.value),
                          switchDuration: const Duration(seconds: 3),
                          children: createBannerView(banners),
                        ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> createBannerView(RxList<SquareBannerBean> banners) {
    AppLogger.d('loadBanner list.length2 = ${banners.length}');
    var views = <Widget>[];
    if (banners.isNotEmpty) {
      for (var b in banners) {
        bool isNet = false;
        if (b.image != null) {
          isNet = b.image!.startsWith('http:') || b.image!.startsWith('https:');
        }
        views.add(GestureDetector(
            onTap: () {
              switch (b.type) {
                case 0:
                  if (b.content != null && (b.content!.startsWith('http://') || b.content!.startsWith('https://'))) {
                    AppLogger.d('banner click ${b.toJson()}');
                    JumpPage.jumpToBrowserMethod(
                      b.content!,
                    );
                  }
                  break;
                case 1:
                  String id = b.id ?? '';
                  if (id.isNotEmpty) {
                    Get.toNamed(Routes.ChannelJoin, arguments: {"channelId": id});
                  }
                  break;
                case 2:
                  String id = b.id ?? '';
                  if (id.isNotEmpty) {
                    var userMessage = UserMessage(
                      chatType: ChatType.singleChat.index,
                      displayName: b.title,
                      userName: id,
                      avatarPath: '',
                    );
                    JumpPage.messgae(userMessage);
                  }
                  break;
              }
            },
            child: Container(
              // height: 140.r,
              // clipBehavior: Clip.hardEdge,
              // decoration:
              //     BoxDecoration(borderRadius: BorderRadius.circular(5.r)),
              child: isNet
                  ? CachedNetworkImage(imageUrl: b.image ?? '', fit: BoxFit.cover)
                  : ExtendedImage.asset(b.image ?? '', fit: BoxFit.cover),
            )));
      }
    }
    AppLogger.d('loadBanner views.length = ${views.length}');

    return views;
  }

  Widget _buildMeetingBtn() {
    return Obx(
      () {
        if (controller.gameInfos.isEmpty) return const SizedBox.shrink();
        var _meetingUrl = controller.gameInfos[controller.selectedGameIndex.value].meetingUrl;
        return _meetingUrl == null || _meetingUrl.isEmpty
            ? const SizedBox.shrink()
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 9.33.r),
                  GestureDetector(
                    onTap: () {
                      controller.openMeeting(_meetingUrl);
                    },
                    child: Container(
                      width: 303.4.r,
                      height: 46.r,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(R.gcMeetingBtn),
                          fit: BoxFit.fill,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SizedBox(width: 18.94.r),
                          Image.asset(
                            R.gcMeetingBtn1,
                            width: 13.35.r,
                            height: 10.56.r,
                          ),
                          SizedBox(width: 6.71.r),
                          Text(
                            L.meeting_in_progress_click_to_enter.tr,
                            style: TextStyle(
                              fontSize: 13.sp,
                              fontWeight: FontWeight.w500,
                              color: AppColors.colorFFCAB692,
                            ),
                          ),
                          const Spacer(),
                          Image.asset(
                            R.gcMeetingBtn2,
                            width: 9.r,
                            height: 10.56.r,
                          ),
                          SizedBox(width: 18.r),
                        ],
                      ),
                    ),
                  ),
                ],
              );
      },
    );
  }

  Widget _buildInfoSection() {
    return Obx(
      () {
        if (controller.gameInfos.isEmpty) return const SizedBox.shrink();
        var _memberStatus = 1; //TODO 获取会员状态 eg. 1=member, 2=non-member
        var _joinStatus = 2; //TODO 获取加入的状态 eg. 1=joined, 2=not joined
        var _gameInfo = controller.gameInfos[controller.selectedGameIndex.value];
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 16.62.r),
            SizedBox(
              width: 292.8.r,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// 头像
                  Container(
                    width: 41.6.r,
                    height: 41.79.r,
                    decoration: BoxDecoration(
                      image: DecorationImage(image: AssetImage(R.gcJoinSectionAvatar1), fit: BoxFit.fill),
                    ),
                    child: Center(
                      child: Container(
                        width: 30.78.r,
                        height: 30.78.r,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.colorFF1F1C1C,
                        ),
                        child: _gameInfo.image == null
                            ? Center(
                                child: Image.asset(
                                  R.gcJoinSectionAvatar2,
                                  width: 13.35.r,
                                  height: 17.34.r,
                                ),
                              )
                            : ClipRRect(
                                borderRadius: BorderRadius.circular(15.39.r),
                                child: CachedNetworkImage(
                                  imageUrl: _gameInfo.image!,
                                  fit: BoxFit.cover,
                                ),
                              ),
                      ),
                    ),
                  ),
                  SizedBox(width: 3.45.r),

                  /// 名字 & 详情 & 会员有效期
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      /// 名字
                      SizedBox(
                        width: 167.r,
                        child: Text(
                          _gameInfo.name,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 17.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.colorFF1F1B1C,
                            height: 1.3,
                          ),
                        ),
                      ),

                      /// 详情
                      if (_gameInfo.description != null && _gameInfo.description!.isNotEmpty)
                        SizedBox(
                          width: 167.r,
                          child: Text(
                            _gameInfo.description!,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 13.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.colorFF6E5F4E,
                              height: 1.3,
                            ),
                          ),
                        ),
                      if (_memberStatus == 1) SizedBox(height: 3.88.r),

                      /// 会员有效期
                      if (_memberStatus == 1)
                        Container(
                          width: 140.r,
                          height: 18.r,
                          padding: EdgeInsets.symmetric(horizontal: 6.25.r, vertical: 1.5.r),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: AppColors.colorFFDFC8B6,
                          ),
                          child: Text(
                            L.member_validity_period.trParams({'date': '2026/05/05'}),
                            style: TextStyle(
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w400,
                              color: const Color.fromARGB(255, 46, 43, 40),
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(width: 10.75.r),

                  /// 加入群聊按钮
                  if (_memberStatus == 1 && _joinStatus != 1)
                    GestureDetector(
                      onTap: () {
                        Get.toNamed(Routes.ChannelJoin, arguments: {"channelId": "_1"});
                      },
                      child: Container(
                        width: 70.r,
                        height: 34.r,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage(R.buttonBg),
                            fit: BoxFit.fill,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              R.gcJoinSectionBtn,
                              width: 22.r,
                              height: 22.r,
                            ),
                            SizedBox(width: 2.r),
                            Text(
                              L.join.tr,
                              style: TextStyle(
                                fontSize: 11.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.colorFFCAB692,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  /// 成为会员按钮
                  if (_memberStatus != 1)
                    GestureDetector(
                      onTap: () async {
                        await showDialog(
                          context: Get.context!,
                          barrierColor: AppColors.colorFF000000.withOpacity(0.85),
                          builder: (context) {
                            return PaymentDialog();
                          },
                        );
                      },
                      child: Container(
                        width: 70.r,
                        height: 34.r,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5.r),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              AppColors.colorFF6D0000,
                              AppColors.colorFF4E0909,
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.color7828231A,
                              blurRadius: 3.r,
                              offset: Offset(0, 2.r),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            L.become_member.tr,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 11.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.colorFFCAB692,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildArticleSection() {
    return Obx(
      () {
        if (controller.gameInfos.isEmpty) return const SizedBox.shrink();
        var _articles = controller.gameInfos[controller.selectedGameIndex.value].articles;
        return _articles == null || _articles.isEmpty
            ? const SizedBox.shrink()
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 13.57.r),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _articles.length,
                    itemBuilder: (context, index) {
                      return _buildArticleItem(index);
                    },
                  ),
                ],
              );
      },
    );
  }

  Widget _buildArticleItem(int index) {
    var _memberStatus = 2; //TODO 获取会员状态 eg. 1=member, 2=non-member
    Article _article = controller.gameInfos[controller.selectedGameIndex.value].articles![index];
    return Center(
      child: GestureDetector(
        onTap: () async {
          if ((_article.isMember ?? false) && _memberStatus != 1) {
            /// 会员文章 && 不是会员
            await showDialog(
              context: Get.context!,
              barrierColor: AppColors.colorFF000000.withOpacity(0.85),
              builder: (context) {
                return Center(
                  child: _buildArticleMemberProhibitDialog(),
                );
              },
            );
            return;
          }
          Get.toNamed(Routes.GameCenterDetailView,
              arguments: {"game_index": controller.selectedGameIndex.value, "article_index": index});
        },
        child: SizedBox(
          width: 285.01.r,
          height: 81.r,
          child: Stack(
            children: [
              /// 文章主体
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 281.95.r,
                  height: 78.06.r,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(R.gcArticleBg),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(width: 2.97.r),

                          /// Article图片
                          Container(
                            width: 83.01.r,
                            height: 56.r,
                            color: AppColors.colorFFFCEAD4,
                            child: _article.imageUrls != null && _article.imageUrls!.isNotEmpty
                                ? CachedNetworkImage(imageUrl: _article.imageUrls![0], fit: BoxFit.cover)
                                : Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        R.gcArticleImageEmpty,
                                        width: 16.r,
                                        height: 16.r,
                                      ),
                                      SizedBox(width: 2.53.r),
                                      Text(
                                        L.image_2.tr,
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          fontWeight: FontWeight.w500,
                                          color: AppColors.colorFFC2AF8C,
                                        ),
                                      ),
                                    ],
                                  ),
                          ),

                          SizedBox(width: 7.87.r),
                          SizedBox(
                            width: 155.r,
                            child: Column(
                              children: [
                                SizedBox(
                                  width: 155.r,
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        if (_article.isPin ?? false)
                                          WidgetSpan(
                                            alignment: PlaceholderAlignment.middle,
                                            child: Image.asset(
                                              R.gcArticlePin,
                                              width: 10.r,
                                              height: 10.r,
                                            ),
                                          ),
                                        if (_article.isPin ?? false) WidgetSpan(child: SizedBox(width: 4.r)),
                                        TextSpan(
                                          text: _article.title ?? '',
                                          style: TextStyle(
                                            height: 1.3,
                                            color: AppColors.colorFF6E5F4E,
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                SizedBox(height: 7.09.r),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                    _article.createdAt ?? '',
                                    style: TextStyle(
                                      fontSize: 10.sp,
                                      fontWeight: FontWeight.w400,
                                      color: AppColors.colorFF83623E,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 5.32.r),
                    ],
                  ),
                ),
              ),

              /// 会员专享标签
              if (_article.isMember ?? false)
                Image.asset(
                  R.gcArticleMember,
                  width: 70.68.r,
                  height: 71.01.r,
                ),

              /// 更多按钮
              Positioned(
                top: 15.r,
                right: 0,
                child: buildArticleMenu(
                  isPin: _article.isPin ?? false,
                  onPinTap: () {
                    controller.setArticleIsPin(controller.selectedGameIndex.value, index, !_article.isPin!);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildArticleMenu({Function()? onPinTap, Function()? onDeleteTap, bool isPin = false}) {
    return Theme(
      data: Theme.of(Get.context!).copyWith(
        dividerTheme: DividerThemeData(
          color: AppColors.colorFF6E5F4E,
        ),
      ),
      child: PopupMenuButton<int>(
        color: AppColors.colorFF26190B,
        padding: EdgeInsets.zero,
        constraints: BoxConstraints(maxWidth: 179.r, minWidth: 179.r),
        child: Padding(
          padding: const EdgeInsets.only(left: 5.0, right: 8.0, top: 20.0, bottom: 30.0).r,
          child: Image.asset(
            R.meetingCardMore,
            width: 14.25.r,
            height: 4.5.r,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10).r,
        ),
        // 在按钮下方显示
        offset: const Offset(0, 50),
        itemBuilder: (context) {
          return [
            PostUtil.buildPopupMenuItem(
              isPin ? L.unpin_top.tr : L.pin_top.tr,
              isPin ? R.gcArticleUnpinOutline : R.gcArticlePinOutline,
              1,
              onPinTap,
            ),
            PopupMenuDivider(height: 0.5.r),
            PostUtil.buildPopupMenuItem(
              L.delete.tr,
              R.iconDeleteBtn,
              2,
              onDeleteTap,
            ),
          ];
        },
        // onSelected: (value) {
        //   _onPostMenuItemSelected(value as int);
        // },
      ),
    );
  }

  Widget _buildCreatePostBtn({Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 58.67.r,
        height: 58.21.r,
        decoration: ShapeDecoration(
          shape: CreateMeetingBtnBorder(),
          image: DecorationImage(image: AssetImage(R.createMeetingBtn), fit: BoxFit.fill),
          shadows: [
            BoxShadow(
              color: AppColors.colorFF6E5F4E,
              blurRadius: 4.r,
              offset: Offset(0, 2),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArticleMemberProhibitDialog() {
    return ScrollStyleDialog(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 39.5).r,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 44.r),

            /// 图标
            BlendMask(
              blendMode: BlendMode.hardLight,
              child: Image.asset(R.gcArticleMemberProhibitIcon, width: 72.26.r, height: 72.21.r),
            ),
            SizedBox(height: 15.r),

            SizedBox(
              width: 274.r,
              child: Text(
                L.note_article_member_prohibit.tr,
                textAlign: TextAlign.left,
                style: TextStyle(
                  height: 1.4,
                  fontSize: 17.sp,
                  color: AppColors.colorFF6E5F4E,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(height: 20.r),

            /// 了解更多按钮
            WoodButton(
              text: L.know_more.tr,
              onTap: () async {
                Get.back();
                await showDialog(
                  context: Get.context!,
                  barrierColor: AppColors.colorFF000000.withOpacity(0.85),
                  builder: (context) {
                    return PaymentDialog();
                  },
                );
              },
            ),

            SizedBox(height: 45.r),
          ],
        ),
      ),
    );
  }
}

class _BannerView extends StatefulWidget {
  final List<Widget> children;

  final Duration switchDuration;

  const _BannerView({
    super.key,
    this.children = const <Widget>[],
    this.switchDuration = const Duration(seconds: 3),
  });

  @override
  State<_BannerView> createState() {
    return _BannerViewState();
  }
}

class _BannerViewState extends State<_BannerView> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  Timer? _timer;
  int _curPageIndex = 0;

  List<Widget> children = [];

  @override
  void initState() {
    super.initState();
    _curPageIndex = 0;

    _tabController = TabController(length: widget.children.length, vsync: this);
    AppLogger.d('widget.children.length==${widget.children.length}');

    /// 添加所有的widget到新建的list中
    children.addAll(widget.children);

    /// 定时器完成自动翻页-只有在大于1时才会有翻页
    if (children.length > 1) {
      children.insert(0, widget.children.last);
      children.add(widget.children.first);

      ///如果大于一页，则会在前后都加一页， 初始页要是 1
      _curPageIndex = 1;
      _timer = Timer.periodic(widget.switchDuration, _nextBanner);
    }

    ///初始页面 指定
    _pageController = PageController(initialPage: _curPageIndex);
  }

  /// 进行翻页的动画
  _nextBanner(Timer timer) {
    _curPageIndex++;
    _curPageIndex = _curPageIndex == children.length ? 0 : _curPageIndex;

    //curve:和android一样 动画插值
    _pageController.animateToPage(_curPageIndex, duration: const Duration(milliseconds: 500), curve: Curves.linear);
  }

  @override
  void dispose() {
    /// 页面销毁时进行回收
    _pageController.dispose();
    _tabController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.colorFFE6E1DD,
      child: Stack(
        children: [
          Listener(

              /// 点击时取消timer效果
              onPointerDown: (_) => _timer?.cancel(),
              onPointerUp: (_) {
                ///重新开启timer
                if (children.length > 1) {
                  _timer = Timer.periodic(widget.switchDuration, _nextBanner);
                }
              },
              child: NotificationListener(
                onNotification: (notification) {
                  if (notification is ScrollUpdateNotification) {
                    ScrollUpdateNotification n = notification;

                    /// 判断是否是一次完整的翻页
                    if (n.metrics.atEdge) {
                      if (_curPageIndex == children.length - 1) {
                        /// 如果是最后一页，那么就跳到第一页
                        _pageController.jumpToPage(1);
                      } else if (_curPageIndex == 0) {
                        /// 如果是第一页，再往前滑动，因为原来的list前后都加了一条数据，所以 -2
                        _pageController.jumpToPage(children.length - 2);
                      }
                    }
                  }
                  return true;
                },
                child: PageView.builder(
                  itemCount: children.length,
                  itemBuilder: (context, index) {
                    /// banner设置点击监听
                    return InkWell(
                      child: children[index],
                      onTap: () {
                        if (kDebugMode) {
                          print("点击Item");
                        }
                      },
                    );
                  },
                  onPageChanged: (index) {
                    _curPageIndex = index;
                    if (index == children.length - 1) {
                      /// 如果是最后一页，那么下面的指示器设置为0的位置
                      _tabController.animateTo(0);
                    } else if (index == 0) {
                      ///如果是第一页再往左滑，那么久设置为指示器最后的位置
                      _tabController.animateTo(_tabController.length - 1);
                    } else {
                      _tabController.animateTo(index - 1);
                    }
                  },
                  controller: _pageController,
                ),
              )),
          Positioned.fill(
            bottom: 8.r,
            child: Align(
              alignment: Alignment.bottomCenter,
              child: SmoothPageIndicator(
                controller: _pageController,
                count: _tabController.length,
                effect: ExpandingDotsEffect(
                  dotWidth: 6.r,
                  dotHeight: 6.r,
                  expansionFactor: 2,
                  spacing: 4.r,
                  activeDotColor: AppColors.colorFFD19832,
                  dotColor: AppColors.white,
                ),
              ),
            ),
          ),
          // Visibility(
          //     visible: children.length > 1,
          //     child: Positioned(
          //       bottom: 8.0,
          //       right: 8.0,
          //       child: TabPageSelector(
          //         controller: _tabController,
          //         indicatorSize: 9,
          //         color: Colors.white,
          //         selectedColor: Colors.blue,
          //       ),
          //     )),
        ],
      ),
    );
  }
}
