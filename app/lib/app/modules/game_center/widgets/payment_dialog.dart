import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/game_center/widgets/payment_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../widgets/brown_text_field.dart';
import '../../../widgets/button/wood_button.dart';
import '../../../widgets/scroll_style_dialog.dart';

class PaymentDialog extends StatelessWidget {
  const PaymentDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PaymentController>(
      init: PaymentController(),
      builder: (controller) {
        return Center(
          child: ScrollStyleDialog(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 29).r,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 45.19.r),

                  /// 标题
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20).r,
                      child: Text(
                        L.redeem_member.tr,
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: AppColors.colorFF6E5F4E,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 5.r),

                  /// 分割线
                  DottedBorder(
                    customPath: (size) => Path()
                      ..moveTo(0, size.height)
                      ..relativeLineTo(size.width, 0),
                    color: AppColors.colorFFCAB692,
                    strokeWidth: 2,
                    dashPattern: [5, 4],
                    child: SizedBox(height: 5.r, width: 370.r),
                  ),
                  SizedBox(height: 12.92.r),

                  /// 在线购买 Label
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20).r,
                      child: Text(
                        L.buy_online.tr,
                        style: TextStyle(
                          fontSize: 17.sp,
                          color: AppColors.colorFF6E5F4E,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 7.31.r),

                  /// 在线购买 选项
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20).r,
                    child: SizedBox(
                      // height: 146.81.r,
                      height: 165.67.r,
                      // width: 301.r,
                      child: Stack(
                        children: [
                          /// 价格选项列表
                          Center(
                            child: SizedBox(
                              height: 146.81.r,
                              child: ScrollConfiguration(
                                behavior: ScrollBehavior().copyWith(overscroll: false),
                                child: ListView.separated(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: controller.membershipPlans.length,
                                  separatorBuilder: (context, index) {
                                    return index != controller.membershipPlans.length - 1
                                        ? SizedBox(width: 8.r)
                                        : const SizedBox.shrink();
                                  },
                                  itemBuilder: (context, index) {
                                    MembershipPlan _plan = controller.membershipPlans[index];
                                    return Obx(
                                      () => _buildMembershipPlanCard(
                                        title: _plan.name,
                                        currency: _plan.currencySymbol,
                                        price: _plan.price.toString(),
                                        isSelected: controller.selectedPlan.value?.id == _plan.id,
                                        onTap: () {
                                          controller.selectPlan(_plan);
                                        },
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),

                          /// Layer 1
                          Align(
                            alignment: Alignment.centerRight,
                            child: Image.asset(
                              R.gcMemberPriceLayer1,
                              width: 3.r,
                              height: 165.67.r,
                            ),
                          ),

                          // /// Layer 2
                          // Align(
                          //   alignment: Alignment.centerRight,
                          //   child: Image.asset(
                          //     R.gcMemberPriceLayer2,
                          //     width: 25.46.r,
                          //     height: 137.19.r,
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 20.05.r),

                  /// 选择支付方式 Label
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20).r,
                      child: Text(
                        L.select_payment_method.tr,
                        style: TextStyle(
                          fontSize: 17.sp,
                          color: AppColors.colorFF6E5F4E,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 4.83.r),

                  /// 选择支付方式 选项
                  GetBuilder<PaymentController>(
                      id: kPaymentMethodsId,
                      builder: (context) {
                        return ScrollConfiguration(
                          behavior: ScrollBehavior().copyWith(overscroll: false),
                          child: ListView.separated(
                            shrinkWrap: true,
                            itemCount: controller.paymentMethods.length,
                            itemBuilder: (context, index) {
                              PaymentMethod method = controller.paymentMethods[index];
                              return Obx(
                                () => _buildPaymentMethodCard(
                                  // image: controller.paymentMethods[index].image,
                                  image: controller.getPaymentMethodIcon(method),
                                  name: controller.getPaymentMethodName(method),
                                  isSelected: controller.selectedPaymentMethod.value == method,
                                  onTap: () {
                                    controller.selectPaymentMethod(method);
                                  },
                                ),
                              );
                            },
                            separatorBuilder: (context, index) {
                              return index != controller.paymentMethods.length - 1
                                  ? SizedBox(height: 4.r)
                                  : const SizedBox.shrink();
                            },
                          ),
                        );
                      }),
                  SizedBox(height: 18.r),

                  // /// 在线兑换 Label
                  // Align(
                  //   alignment: Alignment.centerLeft,
                  //   child: Padding(
                  //     padding: const EdgeInsets.only(left: 20).r,
                  //     child: Text(
                  //       L.online_redeem.tr,
                  //       style: TextStyle(
                  //         fontSize: 17.sp,
                  //         color: AppColors.colorFF6E5F4E,
                  //         fontWeight: FontWeight.w500,
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // SizedBox(height: 5.r),

                  // /// 在线兑换 输入框
                  // BrownTextField(
                  //   hintText: L.please_enter_your_redeem_code.tr,
                  //   maxLength: 12,
                  //   textFieldWidth: 281.r,
                  //   suffixIcon: TextButton(
                  //     onPressed: () {},
                  //     child: Text(
                  //       L.redeem.tr,
                  //       style: TextStyle(
                  //         color: AppColors.colorFFCAB692,
                  //         fontWeight: FontWeight.w500,
                  //         fontSize: 15.sp,
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // SizedBox(height: 7.r),
                  // Align(
                  //   alignment: Alignment.centerLeft,
                  //   child: Padding(
                  //     padding: const EdgeInsets.only(left: 20).r,
                  //     child: SizedBox(
                  //       width: 274.r,
                  //       child: Text(
                  //         L.redeem_note.tr,
                  //         style: TextStyle(
                  //           fontSize: 13.sp,
                  //           fontWeight: FontWeight.w500,
                  //           color: AppColors.colorFF6E5F4E,
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // SizedBox(height: 18.r),

                  /// 确认按钮
                  WoodButton(
                    onTap: () => Get.back(),
                  ),
                  SizedBox(height: 60.r),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMembershipPlanCard(
      {required String title,
      required String currency,
      required String price,
      bool isSelected = false,
      Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: isSelected ? 97.r : 93.r,
        height: isSelected ? 146.81.r : 143.r,
        decoration: BoxDecoration(
          image: DecorationImage(
              image: AssetImage(isSelected ? R.gcMemberPriceOptionActive : R.gcMemberPriceOption), fit: BoxFit.fill),
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.colorFFCAB692,
                ),
              ),
              Text(
                "$currency $price",
                style: TextStyle(
                  fontSize: 17.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.colorFFE6E1DD,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentMethodCard(
      {required String image, required String name, bool isSelected = false, Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 281.r,
            height: 45.r,
            padding: EdgeInsets.symmetric(horizontal: 7.r),
            decoration: BoxDecoration(
              color: AppColors.colorFFFFF6EF,
              borderRadius: BorderRadius.circular(5.r),
              border: Border.all(
                color: AppColors.colorFFCAB692,
                width: 1.5.r,
              ),
            ),
            child: Row(
              children: [
                Image.asset(
                  image,
                  width: 30.r,
                  height: 30.r,
                ),
                SizedBox(width: 6.5.r),
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.colorFF6E5F4E,
                  ),
                ),
                const Spacer(),
                Container(
                  width: 18.r,
                  height: 18.r,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.white,
                    border: Border.all(
                      color: isSelected ? AppColors.colorFF6E5F4E : AppColors.colorFF707070,
                      width: isSelected ? 5.r : 1.r,
                    ),
                  ),
                ),
                SizedBox(width: 5.r),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
