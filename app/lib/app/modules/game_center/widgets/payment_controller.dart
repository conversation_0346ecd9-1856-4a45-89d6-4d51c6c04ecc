import 'dart:io';

import 'package:get/get.dart';

import '../../../../r.dart';

// 支付方式枚举
enum PaymentMethod {
  applePay,
  wechatPay,
  alipay,
}

// 支付状态枚举
enum PaymentStatus {
  idle,
  loading,
  success,
  failed,
  cancelled,
}

const String kPaymentMethodsId = 'payment_methods_get_builder_id';

class PaymentController extends GetxController {
  final Rx<PaymentMethod?> selectedPaymentMethod = Rx<PaymentMethod?>(null);
  final Rx<MembershipPlan?> selectedPlan = Rx<MembershipPlan?>(null);

  final List<MembershipPlan> membershipPlans = [
    MembershipPlan(
      id: 'monthly_vip',
      name: '月卡30天',
      description: '1个月会员权益',
      price: 98,
      duration: 1,
      originalPrice: '98',
      currencySymbol: '¥',
    ),
    MembershipPlan(
      id: 'quarterly_vip',
      name: '季卡90天',
      description: '3个月会员权益',
      price: 265,
      duration: 3,
      originalPrice: '265',
      currencySymbol: '¥',
      isPopular: true,
    ),
    MembershipPlan(
      id: 'yearly_vip',
      name: '年卡365天',
      description: '12个月会员权益',
      price: 980,
      duration: 12,
      originalPrice: '980',
      currencySymbol: '¥',
    ),
  ];

  List<PaymentMethod> paymentMethods = [];

  @override
  void onInit() {
    super.onInit();
    _initPaymentMethod();
  }

  void _initPaymentMethod() async {
    paymentMethods = await getAvailablePaymentMethods();
    update([kPaymentMethodsId]);
  }

  // 选择会员卡
  void selectPlan(MembershipPlan plan) {
    selectedPlan.value = plan;
  }

  // 选择支付方式
  void selectPaymentMethod(PaymentMethod method) {
    selectedPaymentMethod.value = method;
  }

  // 获取可用的支付方式
  Future<List<PaymentMethod>> getAvailablePaymentMethods() async {
    List<PaymentMethod> available = [];

    // 根据平台过滤支付方式
    List<PaymentMethod> platformMethods = [];

    if (Platform.isIOS) {
      // iOS平台：只支持Apple Pay，
      platformMethods = [
        PaymentMethod.applePay,
      ];
    } else if (Platform.isAndroid) {
      // Android平台：支持所有第三方支付，Apple Pay不可用
      platformMethods = [
        PaymentMethod.alipay,
        PaymentMethod.wechatPay,
      ];
    } else {
      // 其他平台（如Web、Windows等）：根据实际情况配置
      platformMethods = [
        PaymentMethod.alipay,
        PaymentMethod.wechatPay,
      ];
    }

    // 检查每个平台支持的支付方式是否真正可用
    for (PaymentMethod method in platformMethods) {
      // if (await isPaymentMethodAvailable(method)) {
      available.add(method);
      // }
    }

    return available;
  }

  // 通过支付方式获取名称
  String getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.applePay:
        return 'Apple支付';
      case PaymentMethod.wechatPay:
        return '微信支付';
      case PaymentMethod.alipay:
        return '支付宝支付';
    }
  }

  // 通过支付方式获取图标
  String getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.applePay:
        return R.paymentMethodApple;
      case PaymentMethod.wechatPay:
        return R.paymentMethodWechat;
      case PaymentMethod.alipay:
        return R.paymentMethodAlipay;
    }
  }
}

// 会员卡模型
class MembershipPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final int duration; // 月数
  final String originalPrice;
  final String currencySymbol;
  final bool isPopular;

  MembershipPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.duration,
    required this.originalPrice,
    required this.currencySymbol,
    this.isPopular = false,
  });
}
