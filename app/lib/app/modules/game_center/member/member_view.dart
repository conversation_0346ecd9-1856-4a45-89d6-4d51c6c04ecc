import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/game_center/member/member_controller.dart';
import 'package:flutter_metatel/app/modules/game_center/widgets/payment_dialog.dart';
import 'package:flutter_metatel/app/widgets/button/wood_button.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../r.dart';
import '../../../widgets/brown_app_bar.dart';
import '../../../widgets/wooden_long_board.dart';

class MemberView extends GetView<MemberController> {
  const MemberView({super.key});

  @override
  Widget build(BuildContext context) {
    return BrownAppBar(
      title: L.game_center.tr,
      dottedColor: AppColors.colorFFC2AF8C,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          image: DecorationImage(image: AssetImage(R.settingBg), fit: BoxFit.cover),
        ),
        child: ScrollConfiguration(
          behavior: ScrollBehavior().copyWith(overscroll: false),
          child: SingleChildScrollView(
            child: SizedBox(
              height: 714.01.r,
              // width: 1.sw,
              child: Stack(
                children: [
                  /// 背景链条左
                  Positioned(
                    top: 0,
                    left: 1.sw / 2 - 115.38.r,
                    child: Image.asset(
                      R.meChain,
                      fit: BoxFit.fill,
                      height: 625.68.r,
                    ),
                  ),

                  /// 背景链条右
                  Positioned(
                    top: 0,
                    right: 1.sw / 2 - 131.27.r,
                    child: Image.asset(
                      R.meChain,
                      fit: BoxFit.fill,
                      height: 625.68.r,
                    ),
                  ),

                  /// 内容
                  Align(
                    alignment: Alignment.topCenter,
                    child: Column(
                      // padding: EdgeInsets.zero,
                      children: [
                        SizedBox(height: 29.53.r),
                        _buildMemberInfo(isMember: false, expiredDate: '2026/05/05'),
                        SizedBox(height: 27.28.r),
                        WoodenLongBoard(
                          text: L.gc_member_detail.tr,
                          width: 326.37.r,
                          padding: EdgeInsets.only(left: 32.12.r),
                        ),
                        SizedBox(height: 12.93.r),
                        _buildPrivilegeBoard(),
                        SizedBox(height: 18.33.r),
                        WoodButton(
                          text: L.redeem_now.tr,
                          buttonWidth: 165.r,
                          onTap: () async {
                            await showDialog(
                              context: context,
                              barrierColor: AppColors.colorFF000000.withOpacity(0.85),
                              builder: (context) {
                                return PaymentDialog();
                              },
                            );
                          },
                        ),
                        SizedBox(height: 20.r),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMemberInfo({bool isMember = false, String? expiredDate}) {
    return SizedBox(
      width: 291.r,
      height: 93.67.r,
      child: Stack(
        children: [
          /// 背景板 2 & role info
          Align(
            alignment: Alignment.centerRight,
            child: Container(
              width: 210.r,
              height: 79.14.r,
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage(R.gcMemberInfoBg2), fit: BoxFit.fill),
              ),
              child: Padding(
                padding: const EdgeInsets.only(left: 25).r,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isMember ? L.gc_member_m.tr : L.gc_member_non_m.tr,
                      style: TextStyle(
                        fontSize: 19.sp,
                        height: 1.3,
                        fontWeight: FontWeight.w500,
                        color: AppColors.colorFFCAB692,
                      ),
                    ),
                    if (isMember) SizedBox(height: 5.r),
                    if (isMember)
                      Container(
                        padding: EdgeInsets.only(left: 6.25.r, top: 1.5.r, bottom: 1.5.r, right: 8.75.r),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(9.r),
                          color: AppColors.colorFFCAB692,
                        ),
                        child: Text(
                          L.gc_member_expired_date.tr + ': ' + (expiredDate ?? ''),
                          style: TextStyle(
                            fontSize: 10.sp,
                            letterSpacing: 0.6,
                            // height: 1.3,
                            fontWeight: FontWeight.bold,
                            color: AppColors.colorFF251F21,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          /// 背景板 1 & badge
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: 92.03.r,
              height: 93.67.r,
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage(R.gcMemberInfoBg1), fit: BoxFit.fill),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.colorFF28231A,
                    blurRadius: 6.r,
                    offset: Offset(4.r, 0),
                  ),
                ],
              ),
              child: Center(
                child: Image.asset(
                  isMember ? R.gcMemberMBadge : R.gcMemberNonMBadge,
                  width: 40.r,
                  height: 46.19.r,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivilegeBoard() {
    return Container(
      width: 364.49.r,
      height: 408.74.r,
      decoration: BoxDecoration(
        image: DecorationImage(image: AssetImage(R.gcMemberPrivilegeBoardBg), fit: BoxFit.fill),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 15.r),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildPrivilegeButton(
                text: L.gc_member_privilege_1.tr,
                child: Image.asset(
                  R.gcMemberPrivilege1,
                  width: 51.52.r,
                  height: 36.28.r,
                ),
                btmSpacing: 24.31,
              ),
              SizedBox(width: 8.r), // Add spacing between buttons
              _buildPrivilegeButton(
                text: L.gc_member_privilege_2.tr,
                child: Image.asset(
                  R.gcMemberPrivilege2,
                  width: 39.9.r,
                  height: 51.89.r,
                ),
                btmSpacing: 13.48,
              ),
              SizedBox(width: 8.r), // Add spacing between buttons
              _buildPrivilegeButton(
                text: L.gc_member_privilege_3.tr,
                child: Image.asset(
                  R.gcMemberPrivilege3,
                  width: 53.59.r,
                  height: 38.91.r,
                ),
                btmSpacing: 17.58,
              ),
            ],
          ),
          SizedBox(height: 39.r),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildPrivilegeButton(
                text: L.gc_member_privilege_4.tr,
                child: Image.asset(
                  R.gcMemberPrivilege4,
                  width: 58.45.r,
                  height: 27.97.r,
                ),
                btmSpacing: 28.11,
              ),
              SizedBox(width: 8.r), // Add spacing between buttons
              _buildPrivilegeButton(
                text: L.gc_member_privilege_5.tr,
                child: Image.asset(
                  R.gcMemberPrivilege5,
                  width: 45.48.r,
                  height: 31.96.r,
                ),
                btmSpacing: 28.11,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrivilegeButton({required String text, required Widget child, double btmSpacing = 20}) {
    return SizedBox(
      width: 99.8.r,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center, // Center align horizontally
        children: [
          // Container(
          //   width: 78.8.r,
          //   child: SimpleShadow(
          //     child: Image.asset(
          //       R.gcMemberPrivilegeBg,
          //       width: 78.8.r,
          //       height: 88.18.r,
          //     ),
          //     opacity: 0.25, // Default: 0.5
          //     color: AppColors.colorFF472501, // Default: Black
          //     offset: Offset(0, 6.r),
          //     // sigma: ,
          //   ),
          // ),
          // PhysicalShape(
          //   clipper: ShapeBorderClipper(
          //     shape: BadgeBorder(),
          //   ),
          //   color: Colors.transparent,
          //   // borderRadius: BorderRadius.circular(10.r),
          //   elevation: 10.r,
          //   // shadowColor: AppColors.colorFF472501,
          //   shadowColor: AppColors.colorFFFFB300,
          //   child: Image.asset(
          //     R.gcMemberPrivilegeBg,
          //     width: 78.8.r,
          //     height: 88.18.r,
          //   ),
          // ),
          // ClipPath(
          //   clipper: BadgeClipper(),
          //   child: Image.asset(
          //     R.gcMemberPrivilegeBg,
          //     width: 78.8.r,
          //     height: 88.18.r,
          //   ),
          // ),
          Container(
            width: 78.8.r,
            height: 88.18.r,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(R.gcMemberPrivilegeBg),
                fit: BoxFit.fill,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                child,
                SizedBox(height: btmSpacing.r),
              ],
            ),
          ),
          SizedBox(height: 3.r),
          SizedBox(
            width: 78.r,
            child: Text(
              text,
              maxLines: 2,
              textAlign: TextAlign.center,
              style: TextStyle(
                height: 1.3,
                fontSize: 15.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.colorFFCAB692,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
