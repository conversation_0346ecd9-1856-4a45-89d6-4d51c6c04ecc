import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/app/data/providers/api/base_dio_api.dart';
import 'package:flutter_metatel/app/modules/uniapp/uni_app_model.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';

import '../../../core/values/config.dart';

class UniAppApi  extends BaseDioClient {
  static String getVersion = '/api/getVersion';

  Dio _createDio() {
    BaseOptions options = BaseOptions();
    options.connectTimeout = const Duration(seconds: 30);
    options.sendTimeout = const Duration(seconds: 30);
    options.contentType = 'application/json; charset=utf-8';
    options.headers = {
      HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
    };
    var dio = super.createDio(options: options);
    return dio;
  }

  /// 获取小程序列表
  Future<Response<UniAppModel?>> getUniAppList() async {
    var url = '${Config.uniappApiUrl()}$getVersion';
    try {
      var response = await _createDio().get(url);
      AppLogger.d(
          'getUniAppList  statusCode:${response.statusCode}   body:${response.data}');
      UniAppModel? data;
      if(response.statusCode==200) {
        data = UniAppModel.fromJson(json.decode(response.data));
        AppLogger.d('getUniAppList  data:${data}   body:${response.data}');
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.d('getUniAppList  e:${e.toString()} ');

      return Response(
        statusCode: -100,
        statusMessage: '',
        data: null,
        requestOptions: RequestOptions(path: ''),
      );
    }
  }

}