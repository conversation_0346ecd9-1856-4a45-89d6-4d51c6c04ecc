import 'package:flutter_metatel/core/utils/app_log.dart';

/// code : 0
/// msg : "success"
/// data : {"name":"潮玩淘","describe":"潮玩孤品猎人，从这里出发","icon":"http://180.74.239.117:7081/icon/__UNI__609C541.svg","appid":"__UNI__609C541","versionName":"1.0.0","versionCode":100,"resource":"http://180.74.239.117:7081/wgt/__UNI__609C541.wgt"}

class UniAppModel {
  UniAppModel({
    int? code,
    String? msg,
    List<UniAppData>? data,
  }) {
    _code = code;
    _msg = msg;
    _data = data;
  }

  UniAppModel.fromJson(dynamic json) {
    _code = json['code'];
    _msg = json['msg'];
    if (json['data'] != null) {
      _data = [];
      json['data'].forEach((v) {
        _data?.add(UniAppData.fromJson(v));
      });
    }
  }

  int? _code;
  String? _msg;
  List<UniAppData>? _data;

  UniAppModel copyWith({
    int? code,
    String? msg,
    List<UniAppData>? data,
  }) =>
      UniAppModel(
        code: code ?? _code,
        msg: msg ?? _msg,
        data: data ?? _data,
      );

  int? get code => _code;

  String? get msg => _msg;

  List<UniAppData>? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = _code;
    map['msg'] = _msg;
    if (_data != null) {
      map['data'] = _data?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// name : "潮玩淘"
/// describe : "潮玩孤品猎人，从这里出发"
/// icon : "http://180.74.239.117:7081/icon/__UNI__609C541.svg"
/// appid : "__UNI__609C541"
/// versionName : "1.0.0"
/// versionCode : 100
/// resource : "http://180.74.239.117:7081/wgt/__UNI__609C541.wgt"
class UniAppData {
  UniAppData({
    String? image,
    String? resource,
    String? introduce,
    String? custom,
    String? appid,
    String? name,
    String? icon,
    String? describe,
    String? versionName,
    num? versionCode,}){
    _image = image;
    _resource = resource;
    _introduce = introduce;
    _custom = custom;
    _appid = appid;
    _name = name;
    _icon = icon;
    _describe = describe;
    _versionName = versionName;
    _versionCode = versionCode;
  }

  UniAppData.fromJson(dynamic json) {
    _image = json['image'];
    _resource = json['resource'];
    _introduce = json['introduce'];
    _custom = json['custom'];
    _appid = json['appid'];
    _name = json['name'];
    _icon = json['icon'];
    _describe = json['describe'];
    _versionName = json['versionName'];
    _versionCode = json['versionCode'];
  }
  String? _image;
  String? _resource;
  String? _introduce;
  String? _custom;
  String? _appid;
  String? _name;
  String? _icon;
  String? _describe;
  String? _versionName;
  num? _versionCode;
  UniAppData copyWith({  String? image,
    String? resource,
    String? introduce,
    String? custom,
    String? appid,
    String? name,
    String? icon,
    String? describe,
    String? versionName,
    num? versionCode,
  }) => UniAppData(  image: image ?? _image,
    resource: resource ?? _resource,
    introduce: introduce ?? _introduce,
    custom: custom ?? _custom,
    appid: appid ?? _appid,
    name: name ?? _name,
    icon: icon ?? _icon,
    describe: describe ?? _describe,
    versionName: versionName ?? _versionName,
    versionCode: versionCode ?? _versionCode,
  );
  String? get image => _image;
  String? get resource => _resource;
  String? get introduce => _introduce;
  String? get custom => _custom;
  String? get appid => _appid;
  String? get name => _name;
  String? get icon => _icon;
  String? get describe => _describe;
  String? get versionName => _versionName;
  num? get versionCode => _versionCode;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['image'] = _image;
    map['resource'] = _resource;
    map['introduce'] = _introduce;
    map['custom'] = _custom;
    map['appid'] = _appid;
    map['name'] = _name;
    map['icon'] = _icon;
    map['describe'] = _describe;
    map['versionName'] = _versionName;
    map['versionCode'] = _versionCode;
    return map;
  }

}
