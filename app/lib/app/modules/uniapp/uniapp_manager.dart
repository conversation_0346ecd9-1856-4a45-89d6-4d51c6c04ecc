import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_browser/flutter_browser.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/avatar_model.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/data/services/event_service.dart';
import 'package:flutter_metatel/app/modules/uniapp/uni_app_model.dart';
import 'package:flutter_metatel/app/modules/uniapp/uniapp_api.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/screen.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

import '../../../core/task/time_task.dart';
import '../../../core/utils/jump.dart';
import '../../../core/values/config.dart';
import '../../data/services/chatio_service.dart';

class UniAppManager {
  static String tag = 'UniAppManager';

  UniAppManager._() {
    _init();
  }

  static UniAppManager? instance;

  static UniAppManager _adsTask() {
    instance ??= UniAppManager._();
    return instance!;
  }

  factory UniAppManager() => _adsTask();

  //删除全部小程序
  delete() {
    var path = getUniAppParentDirPath();
    var file = Directory(path);
    if (file.existsSync()) {
      file.deleteSync(recursive: true);
      toast('删除成功');
    }
  }

  //添加本地小程序
  addUniApp() async {
    var result = await FilePicker.platform.pickFiles(allowMultiple: true);
    if (result == null) {
      return;
    }
    List<String?> filePaths = result.paths.map((path) => path).toList();
    if (filePaths.length > 1) {
      toast('只能选择一个');
      return;
    }
    var filePath = filePaths.first;
    if (!filePath!.endsWith('.wgt')) {
      toast('文件格式不正确');
      return;
    }
    var p = filePath.split('/');
    var appid = p.last;
    appid = appid.replaceAll('.wgt', '');
    AppLogger.d('_addUniApp appid=$appid');
    Screen.installMP(appid: appid, wgtPath: filePath);
  }

  _init() async {
    AppLogger.d('$tag loadTest init start');

    //初始化小程序
    var res = await Screen.init(receive: (event) async {
      if (event['event'] == 'getUserInfo') {
        sendUserInfo(event['appid']);
        // Screen.callbackMP(appid: event['appid'], event: 'apptoUniapp',data: {"data": 11111,"value":3333333});
      } else if (event['event'] == 'shareMiniProgram') {
        Map data = event['data'];
        Screen.hideMP(appid: event['appid']);
        await onShareUniapp(json.encode(data));
        await Screen.callbackMP(
            appid: event['appid'], event: 'shareMiniProgram', data: {'code':0});
        await Screen.openMP(appid: event['appid'],);
      }else if (event['event'] == 'sendMessage') {
        Map data = event['data'];
        AppLogger.d('$tag sendMessage data=$data');
        Screen.hideMP(appid: event['appid']);
        await submitMessage(data);
        // await Screen.openMP(appid: event['appid'],);

      }
      AppLogger.d('$tag loadTest init event =${event['event']}');
    });
    AppLogger.d('$tag loadTest init res=$res');
  }

  //打开小程序
  openUniapp(String appId,{dynamic data}) async {
    var res = await Screen.openMP(appid: appId,config:{'arguments':{'arg':data??{}}});
    AppLogger.d('$tag loadTest openMP res=$res');
  }

  //发送用户信息
  sendUserInfo(String appid) {
    Screen.callbackMP(
        appid: appid, event: 'userInfo', data: _getUserInfo());
  }

  //获取版本号
  Future<String?> getVersion({String? appid}) async {
    var res = await Screen.versionMP(appid: appid ?? '__UNI__609C541');
    if(res is Map) {
      return json.encode(res);
    }
    AppLogger.d('$tag loadTest versionMP res=$res');
    return res;
  }

  Future<bool> needUpdateResource(UniAppData data) async {
    bool needupdata = true;
    var res = await getVersion(appid: data.appid);
    AppLogger.d('$tag loadTest versionMP res=$res');
    if (res != null && data.versionCode != null) {
      var r = json.decode(res);
      if (r is Map) {
        var code = r['code'];
        if ((data.versionCode ?? 0) <= code) {
          needupdata = false;
        }
      }
    }
    AppLogger.d('$tag loadTest needupdata=$needupdata');
    return needupdata;
  }

  Future<bool> appIsInstalled(String appid) async {
    var res = await Screen.checkMP(appid: appid);
    AppLogger.d('$tag loadTest appIsInstalled res=$res');
    return res;
  }

  Map<String, dynamic> _getUserInfo() {
    Map<String, dynamic> map = {};
    AvatarModel avatar = AvatarModel.fromJson(
        Get.find<AppConfigService>().getMySelfAvatarInfo() ?? {});
    map['user_name'] = Get.find<AppConfigService>().getUserName();
    map['nick_name'] = Get.find<AppConfigService>().getMySelfDisplayName();
    map['icon'] = avatar.avatarUrl;
    map['phone'] = Get.find<AppConfigService>().getPhone();
    map['language'] = currentLanguageIsSimpleChinese() ? 'zh' : 'en';
    AppLogger.d('$tag getUserInfo info=$map');
    return map;
  }

  getUniAppList() async {
    var res = await Get.find<UniAppApi>().getUniAppList();
    AppLogger.d('$tag getUniAppList res=${res.data}');

    if (res.data != null && res.data?.code == 0) {
      var uniapplist = res.data?.data;
      var hotUniappList = <BrowserHot>[];
      uniapplist?.forEach((d) {
        hotUniappList.add(BrowserHot(
            resource: d.resource,
            title: d.name,
            subTitle: d.describe,
            versionCode: d.versionCode?.toInt(),
            versionName: d.versionName,
            icon: d.icon,
            type: 1,
            appId: d.appid));
      });

      BrowserConfig.uniappList.value = hotUniappList;
      AppLogger.d('$tag getUniAppList uniapplist=${uniapplist?.length}');

      if (uniapplist?.isNotEmpty ?? false) {
        for (var data in uniapplist!) {
          if (await needUpdateResource(data)) {
            await downLoad(data);
          }
        }
      }
    }
  }

  downLoad(UniAppData data) async {
    var url = data.resource;
    if (url?.isNotEmpty ?? false) {
      AppLogger.d('$tag installMP url=$url');
      var path = await downLoadFile(url ?? '', 'wgt');
      AppLogger.d('$tag downLoad path=$path');
      var filePath = appSupporAbsolutePath(path) ?? '';
      AppLogger.d('$tag downLoad p=$filePath');
      var appid = data.appid ?? '';

      if (await needUpdateResource(data) &&
          filePath.isNotEmpty &&
          appid.isNotEmpty) {
        if (File(filePath).existsSync()) {
          AppLogger.d('$tag installMP appid=$appid');

          var res = await Screen.installMP(appid: appid, wgtPath: filePath);
          AppLogger.d('$tag installMP res=$res');
        }
      }
    }
  }

  onShareUniapp(String uniapp) async {
    var messageEvent = MessageEvent(
      uuid(),
      owner: '',
      type: MessageType.uniapp,
      chatType: ChatType.singleChat,
      body: uniapp,
      direction: Direction.outGoing,
      dateTime: TimeTask.instance.getNowDateTime(),
    );
    await shareForwardMessage(Get.context!, [messageEvent]);
  }

  jumpUniapp(Map<String, dynamic> uniapp) async {
    var data = UniAppData.fromJson(uniapp);

      if (await needUpdateResource(data)) {
        await downLoad(data);
        var appid = data.appid;
        if (appid == null) {
          return;
        }
        if (await appIsInstalled(appid)) {
          openUniapp(appid);
        }
      } else {
        var appid = data.appid;
        openUniapp(appid??'',data: data.custom);
      }
  }
  submitMessage(Map<dynamic,dynamic> info) async {
    if (info.isNotEmpty) {
      String user = info['user_name'] ?? '';
      int chatType = info['chat_type'] ?? 0;
      String displayName = '';
      if (user.isNotEmpty) {
        if (user.length > 5) {
          displayName = user.substring(0, 5);
        }
        AppLogger.d('submitMessage displayName=$displayName');
        var userMessage = UserMessage(
            chatType: chatType,
            displayName: displayName,
            userName: user,
            avatarPath: '');
        // Get.toNamed(Routes.MESSAGE, arguments: userMessage);
        JumpPage.messgae(userMessage);
      }
    }
  }
}
