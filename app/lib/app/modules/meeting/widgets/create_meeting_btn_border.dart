import 'package:flutter/material.dart';

class CreateMeetingBtnBorder extends ShapeBorder{
  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.zero;

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return _createMeetingBtnPath(rect);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    return _createMeetingBtnPath(rect);
  }

  Path _createMeetingBtnPath(Rect rect) {
    final path = Path();
    path.moveTo((rect.left + rect.width / 5), rect.top);
    path.lineTo(rect.right, rect.top);
    path.lineTo(rect.right, rect.bottom);
    path.lineTo(rect.left + rect.width / 5, rect.bottom);
    path.lineTo(rect.left, rect.centerLeft.dy);
    
    path.close();

    return path;
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
  }

  @override
  ShapeBorder scale(double t) => this;
}