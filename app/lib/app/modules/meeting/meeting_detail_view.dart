import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/button/wood_button.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../r.dart';
import '../../data/models/meeting.dart';
import '../../widgets/brown_app_bar.dart';
import '../../widgets/card_container.dart';
import 'meeting_controller.dart';
import 'widgets/detail_info_item.dart';

class MeetingDetailView extends StatelessWidget {
  const MeetingDetailView({super.key,
    required this.actualIndex,
    required this.meeting,});  
  final int actualIndex;
  final Meeting meeting;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MeetingController>();
    return BrownAppBar(
      title: L.meeting_detail.tr,
      suffix: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          
          /// Copy 按钮
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              controller.onCopyMeetingTap(meeting);
            },
            child: Padding(
              padding: const EdgeInsets.all(9).r,
              child: Image.asset(
                R.iconMeetingCopy,
                width: 22.r,
                height: 22.r,
                color: AppColors.colorFFCAB692,
              ),
            ),
          ),

          /// 编辑按钮
          if(controller.isHost(meeting.hostId ?? ''))
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                Get.back();
                controller.onEditMeetingTap(meeting);
              },
              child: Padding(
                padding: const EdgeInsets.all(9).r,
                child: Image.asset(
                  R.meetingEdit,
                  width: 22.r,
                  height: 22.r,
                  color: AppColors.colorFFCAB692,
                ),
              ),
            ),

          /// 分享按钮
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              controller.onShareTap(meeting);
            },
            child: Padding(
              padding: const EdgeInsets.all(9).r,
              child: Image.asset(
                R.meetingShare,
                width: 22.r,
                height: 22.r,
                color: AppColors.colorFFCAB692,
              ),
            ),
          ),
        ],
      ),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(image: AssetImage(R.meetingBg), fit: BoxFit.cover),
        ),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    final controller = Get.find<MeetingController>();
    return ScrollConfiguration(
      behavior: ScrollBehavior().copyWith(overscroll: false),
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 9.r),
          child: Column(
            children: [
              SizedBox(height: 11.r),
              CardContainer(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 23.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: 41.r),
                      Text(
                        meeting.title ?? '',
                        maxLines: 1,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontSize: 17.sp,
                          fontWeight: FontWeight.w500,
                          overflow: TextOverflow.ellipsis,
                          color: AppColors.colorFF1F1B1C,
                        ),
                      ),
                      SizedBox(height: 1.r),

                      /// 会议说明
                      if(meeting.description != null && meeting.description!.isNotEmpty)
                        ConstrainedBox(
                          constraints: BoxConstraints(maxHeight: 97.r),
                          child: SingleChildScrollView(
                            child: Text(
                              meeting.description ?? '',
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                height: 1.2,
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w500,
                                color: AppColors.colorFF6E5F4E,
                              ),
                            ),
                          ),
                        ),
                      SizedBox(height: 36.r),
                      
                      /// 主持人
                      DetailInfoItem(title: L.host.tr, content: meeting.hostName),
                      SizedBox(height: 36.r),

                      /// 日期
                      DetailInfoItem(
                        title: L.date.tr,
                        content: controller.getFormattedDate(meeting.startTime),
                      ),
                      SizedBox(height: 36.r),
                      
                      /// 开始时间，结束时间
                      Row(
                        children: [
                          Expanded(
                            flex: 1,
                            child: DetailInfoItem(
                              title: L.start_time.tr,
                              content: controller.getFormattedTime(meeting.startTime),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: DetailInfoItem(
                              title: L.end_time.tr,
                              content: controller.getFormattedTime(meeting.endTime),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 36.r),
                      
                      /// 密码
                      DetailInfoItem(title: L.passcode.tr, content: (meeting.passcode == null || meeting.passcode!.isEmpty) ? L.not_set_password_yet.tr : meeting.passcode),
                      SizedBox(height: 36.r),

                      /// 会议链接
                      GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          controller.onCopyMeetingUrlTap(
                              controller.isHost(meeting.hostId ?? '')
                                  ? meeting.hostOpenMeeting ?? ''
                                  : meeting.meetingLink ?? '');
                        },
                        child: DetailInfoItem(
                          title: controller.isHost(meeting.hostId ?? '')
                              ? L.host_meeting_url.tr
                              : L.meeting.tr,
                          customContent: SizedBox(
                            width: 324.r,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  (controller.isHost(meeting.hostId ?? '')
                                    ? meeting.hostOpenMeeting
                                    : meeting.meetingLink) ?? '',
                                  textAlign: TextAlign.left,
                                  style: TextStyle(
                                    height: 1.3,
                                    fontSize: 15.sp,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.colorFF39517A,
                                  ),
                                ),
                                SizedBox(height: 3.r),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image.asset(
                                      R.iconMeetingCopy,
                                      width: 14.r,
                                      height: 14.r,
                                      color: AppColors.colorFF83623E,
                                    ),
                                    SizedBox(width: 7.r),
                                    Text(
                                      L.copy_link.tr,
                                      style: TextStyle(
                                        fontSize: 13.sp,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.colorFF83623E,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      if (controller.isHost(meeting.hostId ?? '')) SizedBox(height: 12.r),
                      if (controller.isHost(meeting.hostId ?? ''))
                        Text(
                          L.host_meeting_url_note.tr,
                          style: TextStyle(
                            height: 1.2,
                            fontSize: 14.sp,
                            color: Colors.red,
                            fontWeight: FontWeight.w500,
                            fontStyle: FontStyle.italic,
                          ),
                        ),

                      SizedBox(height: 36.r),

                      /// 是否任何用户都能打开
                      if (meeting.isAnyUserCanOpenMeeting ?? false)
                        Text(
                          "*** ${L.any_user_can_open_meeting_indicator.tr}",
                          style: TextStyle(
                            fontSize: 13.sp,
                            fontStyle: FontStyle.italic,
                            fontWeight: FontWeight.w400,
                            color: AppColors.colorFF6E5F4E,
                          ),
                        ),
                      if (meeting.isAnyUserCanOpenMeeting ?? false)
                        SizedBox(height: 36.r),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 59.r),
              WoodButton(
                text: controller.isHost(meeting.hostId!)
                    ? L.start_meeting.tr
                    : L.join_now_2.tr,
                buttonWidth: 165.r,
                onTap: () {
                  controller.onStartMeetingTap(meeting);
                },
              ),
              SizedBox(height: 60.r),
            ],
          ),
        ),
      ),
    );
  }
}