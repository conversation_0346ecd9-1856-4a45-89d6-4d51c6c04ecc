import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/gradient_progress_bar.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/config.dart';
import '../../../../../r.dart';
// import '../../../../../routes/pages.dart';
import '../../account_service.dart';
// import 'mnemonic_view_controller.dart';

class MnemonicPageTwo extends StatefulWidget {
  MnemonicPageTwo(this.mnemonic,{Key? key}) : super(key: key);
  String mnemonic;

  @override
  State<StatefulWidget> createState() => _MnemonicPage();
}

class _MnemonicPage extends State<MnemonicPageTwo> {
  final ValueNotifier<RxDouble> countdown = ValueNotifier<RxDouble>(0.1.obs);
  Timer? _timer;
  @override
  void dispose() {
    super.dispose();
    _stopTimer();
  }

  @override
  void initState() {
    super.initState();
    var works = widget.mnemonic;
    Get.find<AccountService>().login(
        works,
        action:1,
        onError: () {
          //   while(!(Get.currentRoute == Routes.PhonePage)) {
          //     Get.back();
          // }
        }
    );
    _startTimer();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Container(
        width: 1.sw,
        height: 1.sh,
        // color: AppColors.backgroundGray,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(R.loginscreenBg),
            fit: BoxFit.cover,
          ),
        ),
        child: Column(
          children: [
            SizedBox(height: 108.r,),
            Image.asset(
              R.logoBadge,
              width: 205.r,
            ),
            SizedBox(height: 42.r,),

            Text(
              L.generating_account.tr, // Your custom text
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppColors.colorFF28231A, // Set the text color, e.g., white
                fontSize: 17.sp, // Adjust the font size as needed
                fontWeight: FontWeight.w500,
                // fontFamily: Config.secondFontFamily,
              ),
            ),
            SizedBox(height: 38.8.r,),
            SizedBox(
              width: 312.r,
              child: Obx(() => GradientProgressBar(
              value: countdown.value.value, // 进度值 (0.0 ~ 1.0)
              // height: 16, // 进度条高度
              // borderRadius: 14, // 圆角半径
              // borderWidth: 1, // 边框宽度
            )),),
            const Spacer(),
            Image.asset(
              R.textLogoRed,
              height: 94.66.r,
            ),
            SizedBox(height: 52.r,),
          ],
        ),
      ),
    );
  }
  _stopTimer() {
    countdown.dispose();
  }

  _startTimer() {
    _timer?.cancel();
    countdown.value.value = 0.1;
    _timer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (countdown.value.value < 0.8) {
        countdown.value.value += 0.03;
      } else if (countdown.value.value < 0.9) {
        countdown.value.value += 0.01;
      } else if (countdown.value.value < 0.99) {
        countdown.value.value += 0.001;
      }
      if(countdown.value.value>=1) {
        countdown.value.value = 0.99;
      }
    });
  }


}
