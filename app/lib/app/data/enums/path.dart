/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-07 12:30:46
 * @Description  : 获取系统相关的目录
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-20 12:03:28
 * @FilePath     : /flutter_metatel/lib/app/data/enums/path.dart
 */

import 'dart:io';

import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:path_provider/path_provider.dart';

Directory? _tempDir;
Directory? get appTempDir => _tempDir;

Directory? _supportDir;
Directory? get appSupportDir => _supportDir;

Directory? _documentDir;
Directory? get documentDir => _documentDir;

Future<void> initConfigPath() async {
  _tempDir = await getTemporaryDirectory();
  _documentDir = await getApplicationDocumentsDirectory();
  _supportDir = await getApplicationSupportDirectory();
  if (File('${_documentDir?.path}/db.sqlite').existsSync()) {
    _supportDir = _documentDir;
    return;
  }
}
