// ignore_for_file: constant_identifier_names

/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-11 12:10:24
 * @Description  : 枚举相关的类
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-17 15:45:56
 * @FilePath     : /flutter_metatel/lib/app/data/enums/enum.dart
 */

enum BubbleItemState {
  LOAD, // 本地处理, 处理本地文件 0
  SENT, // 开始发送 1
  DELIVERED, // 发送到服务器 2
  SEEN, // 已读 3
  FAILED, // 发送失败 4
  SENSITIVE, // 敏感词 5
  none, // 无状态 6
  unsent, //未发送 7
}

// 0 文本 1 图片、2音频、3视频、4文件
enum MessageType {
  text, // 文本 0
  image, // 图片 1
  audio, // 音频 2
  video, // 视频 3
  file, // 文件 4
  other, // 其他消息 5
  userinfo, // 用户信息 6
  contactCard, // 联系人名片 7
  none, // 无 8
  group_operation, // 群操作9
  call, // 电话 10
  fail, // 接收失败11
  tip, // 提示 12
  ////分界线，上面是和数据库对应，只能加不能插入//////////////
  date, // 用于显示日期 13
  channelOpera, // 频道操作 14
  channelCard, // 频道名片15
  sticker, //表情贴纸16
  stickerDefault, //默认表情贴纸17
  msgMergeForward, //合并转发消息18
  fileHelperOpera, //文件传输助手操作19
  stickerDefaultRabbit, //默认兔年定制表情贴纸20
  stickerDefaultEmoji, //默认兔年定制emoji表情贴纸21
  pcOperate, //pc操作事件22
  walletAddress, //钱包地址 23
  moneyExchange, //币币兑换 24
  walletTransaction, //钱包交易 25
  walletBill, //账单 26
  other2, //废弃红包27 类型
  hongbao, //红包 28
  meeting, //会议 29
  uniapp, //小程序 30
}

enum MessageItemType {
  message, // 消息
  date, // 日期
}

enum CommentContactState { NONE, SINGLE, MULTIPLE, BACK }

enum ChatType {
  singleChat, // 单聊
  groupChat, // 群聊
  channelChat, // 频道
  officialChat, //官方号
  other, //处理默认头像为特殊图片的
}

enum ContactState {
  friend, // 好友
  notFriend, // 不是好友
}

enum ChannelState {
  invalid, // 无效
  normal, // 正常
}

enum ChatOwnState {
  none, // 无
  ing, // 正在
  success, // 成功
}

enum PushType {
  none, // 无
  message, // 消息
  rtc, // 即时通话
}

/// 频道成员角色
enum ChannelMemberRole {
  ordinary, // 普通
  administrator, // 管理员
  owner, // 群主
}

enum ShareCurrentType {
  none,
  joined,
  leaderJoin,
}

enum TabooType {
  none,
  taboo,
  taboo_all,
  groupOnlySelf, // 群里只剩自己
}

/// 频道属性
enum ChannelAttribute {
  ordinary, // 普通群
  dao, // DAO群
}

/// 助记词显示界面类型
enum MnemonicViewType {
  registered, // 注册
  export, // 导出
}

/// 保存文件类型
enum SaveFileType {
  file,

  ///文件
  image,

  /// 图片
  video,

  ///视频
}

enum PayIdentifyType {
  password,
  fingerprint,
  faceId,
}

enum HongBaoState {
  beSent,
  paymentSuccessful,
  receivedOrCompleted,
}

/// 频道动态：帖的更多操作
enum MomentPostOption { edit, delete }

/// 频道动态：评论的更多操作
enum MomentCommentOption { delete }

/// 个人详情：更多操作
enum ContactDetailMoreOption {
  mute,
  shareContact,
  clearHistory,
  deleteContact,
  blacklist,
  groupAdminRights,
  groupMute,
  groupRecall,
  groupKick
}

/// 群详情：更多操作
enum GroupDetailMoreOption { mute, share, clearHistory, leave, disband }
