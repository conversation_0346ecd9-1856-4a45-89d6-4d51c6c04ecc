import 'package:get/get.dart';

import '../../../core/values/config.dart';
import '../models/avatar_model.dart';
import '../models/mobile_verify_model.dart';
import '../models/real_name_verify_model.dart';
import '../models/res/emoticon_res_model.dart';
import '../providers/db/database.dart';
import '../services/event_service.dart';

/// 清空聊天记录事件
class ClearChatHistoryEvent {
  ClearChatHistoryEvent(this.userName);
  final String userName;
}

class ContactDataUpdateEvent {
  ContactDataUpdateEvent(this.contactData,{this.server = true});
  final ContactData contactData;
  bool server;
}

class ContactDataMyselfUpdateEvent {
  ContactDataMyselfUpdateEvent();
}

class MySelfAvatarUpdateEvent {
  MySelfAvatarUpdateEvent(this.avatarModel);
  final AvatarModel avatarModel;
}

/// 同步频道列表事件
class SyncChannelsEvent {
  SyncChannelsEvent();
}

/// 同步频道消息事件
class SyncChannelMessageEvent {
  SyncChannelMessageEvent();
}

/// 未授权事件
class UnauthorizedEvent {
  UnauthorizedEvent(this.hint);
  final String hint;
}

/// 会话界面通知消息类
class SessionNoticeEvent {
  SessionNoticeEvent(this.type, this.end);

  /// 事件类型
  final int type;

  /// 是否结束 true:结束
  final bool end;
}

/// token不合法
class TokenErrorEvent {
  TokenErrorEvent();
}

/// 频道信息更新事件
class ChannelOrGroupInfoUpdateEvent {
  ChannelOrGroupInfoUpdateEvent(
      {this.id,
      this.title,
      this.avatarPath,
      this.type = 2,
      this.announcement,
      this.invalid = true});
  final String? id;
  final int type;
  final String? title;
  final String? announcement;
  final String? avatarPath;
  final bool invalid;
}

/// 聊天背景更新事件
class ChatBackgroundUpdateEvent {
  ChatBackgroundUpdateEvent();
}

/// 接收到消息
class MessageRecvEvent {
  MessageRecvEvent(this.exist,
      this.messages,
      {this.isSync = false});
  final bool exist;
  final bool isSync;
  List<MessageData> messages;
}

/// 消息草稿变化事件
class MsgDraftChangeEvent {
  MsgDraftChangeEvent(this.name);
  final String name;
}

//耳机切换事件
class HeadSetChangeEvent {
  final bool isConnected;
  HeadSetChangeEvent(this.isConnected);
}

class ChannelOptionEvent {
  final String? action;
  final String? channelId;
  ChannelOptionEvent(this.action, this.channelId);
}

/// 频道消息撤回事件
class ChannelMessageUndoEvent {
  ChannelMessageUndoEvent(this.channelId, this.messages);
  final String channelId;
  final List<MessageData> messages;
}

class NotReadEvent {}

/// 消息转发事件
class MessageForwardEvent {
  MessageForwardEvent(this.data);
  final MessageEvent data;
}

///
class UpdateMsgByMsgIdEvent {
  UpdateMsgByMsgIdEvent(this.msgId);
  final String msgId;
}

///
class UpdateOneEmojiMsgByMsgIdEvent {
  UpdateOneEmojiMsgByMsgIdEvent(this.msgId);
  final String msgId;
}

///
class UpdateOneVoiceMessageHasReadByMsgIdEvent {
  UpdateOneVoiceMessageHasReadByMsgIdEvent(this.msgId);
  final String msgId;
}

/// 预览文件退出事件
class FilePreviewExitEvent {
  FilePreviewExitEvent(this.ids);
  final List<String> ids;
}

/// 助记词恢复顶替另一个登陆设备事件
class MnemonicRestoreReplaceContinueEvent {
  MnemonicRestoreReplaceContinueEvent();
}

/// 取消消息置顶
class CancelMsgTopEvent {
  CancelMsgTopEvent(this.username, this.msgid);
  final String username;
  final String msgid;
}

/// 文件传输助手是否状态事件
class FileHelperStateEvent {
  FileHelperStateEvent(this.isOnline);
  final bool isOnline;
}

/// 文件传输助手操作消息事件
class FileHelperOptionMessageEvent {
  FileHelperOptionMessageEvent(this.action);
  final String action;
}

/// 实名认证事件
class RealNameVerifyEvent {
  RealNameVerifyEvent(this.data);

  RealNameVerifyModel data;
}

/// 手机号验证
class MobileVerifyEvent {
  MobileVerifyEvent(this.data);

  MobileVerifyModel data;
}
//更新群公告
class UpdateAnnouncementEvent{

}

//更新DDC藏品
class UpdateDdcCollectionEvent{

}
//更新emoji
class UpdateEmoticonEvent{
  UpdateEmoticonEvent(this.userName,this.list);
  String userName;
 List<EmBean>?list;
}

//分享事件
class ShareEvent{

}
class BlacklistEvent{
  BlacklistEvent(this.isBlack);
  final bool isBlack;
}

class NeedUpdate{

}

class MiningTaskEvent {
  MiningTaskEvent(this.type);

  final String type;
}

class UpdateFileState {
  UpdateFileState(this.event,{this.updateUi=true,this.mergeMsg=false});
  final bool updateUi;
  final bool mergeMsg;
  final MessageEvent event;
}
///主界面更新事件
class HomePageUpdate {
  HomePageUpdate();
}

class CipherErrorEvent {
  final String msgId;
  final String? filePath;

  CipherErrorEvent(this.msgId, this.filePath);
}

class UpdateMuteChangeEvent {
  final String userName;

  UpdateMuteChangeEvent(this.userName);
}

class AudioPlayEvent {
  final String msgId;
  final bool isClick;

  AudioPlayEvent(this.msgId, {this.isClick = false});
}
class ContactUpdateEvent {
  final String userName;
  final String name;
  final String? avatar;
  ContactUpdateEvent(this.userName,this.name,this.avatar);
}
class UpdateLanguageEvent {

}

/// 消息删除事件
class MessageDelEvent {
  MessageDelEvent(this.userName, this.messages);
  final String userName;
  final List<MessageData> messages;
}
class AdsUpdatedEvent {
  AdsUpdatedEvent();
}
class PageLoadSuccessEvent {
  PageLoadSuccessEvent();
}

class RepurchaseSuccessEvent {
  double? totalCount;
  RepurchaseSuccessEvent(this.totalCount);
}

class LastMessageEvent{
  LastMessageEvent(this.id);
  String id;
}

class UpdateStakingEvent{
  UpdateStakingEvent();
}