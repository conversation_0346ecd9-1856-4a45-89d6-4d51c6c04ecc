import 'dart:convert';

import 'package:get/get.dart';

import '../../../core/utils/app_log.dart';
import '../models/user_status.dart';
import 'config_service.dart';

// ===============================
// 2. Service 层 - 纯业务逻辑
// ===============================
class UserStatusService extends GetxService {
  var _appConfigService = Get.find<AppConfigService>();

  // 获取状态 API 调用
  Future<UserStatus> fetchUserStatusFromApi() async {
    try {
      // 模拟 API 调用
      await Future.delayed(Duration(seconds: 1));

      // 实际实现：
      // final response = await dio.get('/api/user/status');
      // return UserStatus.fromJson(response.data);

      // 模拟返回数据
      return UserStatus.member(
        // membershipLevel: "VIP",
        membershipExpiry: DateTime.now().add(Duration(days: 30)),
      );
    } catch (e) {
      throw Exception('Service Error Catch<fetchUserStatusFromApi>: $e');
    }
  }

  // 本地存储操作
  Future<void> saveUserStatusToLocal(UserStatus status) async {
    try {
      await _appConfigService.saveUserStatus(json.encode(status.toJson()));
    } catch (e) {
      throw Exception('Service Error Catch<saveUserStatusToLocal>: $e');
    }
  }

  Future<UserStatus?> loadUserStatusFromLocal() async {
    try {
      // final box = GetStorage();
      // final data = box.read('user_status');
      final data = _appConfigService.readUserStatus();
      return data != null ? UserStatus.fromJson(json.decode(data)) : null;
    } catch (e) {
      AppLogger.e('Service Error Catch<loadUserStatusFromLocal>: $e');
      return null;
    }
  }

  Future<void> clearLocalUserStatus() async {
    try {
      // final box = GetStorage();
      // await box.remove('user_status');
      await _appConfigService.removeUserStatus();
    } catch (e) {
      AppLogger.e('Service Error Catch<clearLocalUserStatus>: $e');
    }
  }

  // 会员升级 API
  // Future<UserStatus> upgradeMembership(String membershipLevel) async {
  //   try {
  //     // 实际实现：
  //     // final response = await dio.post('/api/user/upgrade', data: {
  //     //   'membershipLevel': membershipLevel,
  //     // });
  //     // return UserStatus.fromJson(response.data);

  //     // 模拟
  //     await Future.delayed(Duration(seconds: 1));
  //     return UserStatus.member(
  //       membershipLevel: membershipLevel,
  //       membershipExpiry: DateTime.now().add(Duration(days: 365)),
  //     );
  //   } catch (e) {
  //     throw Exception('升级会员失败: $e');
  //   }
  // }
}
