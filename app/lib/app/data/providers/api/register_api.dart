import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/models/res/mex_get_user_info_res_model.dart';
import 'package:flutter_metatel/app/data/models/res/node_conf.dart';
import 'package:flutter_metatel/app/data/models/res/oauth2_authorize.model.dart';
import 'package:flutter_metatel/app/data/models/sts_info_model.dart';
import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:flutter_metatel/app/data/services/emoji_manage_service.dart';
import 'package:flutter_metatel/app/modules/dao/dao_config.dart';
import 'package:flutter_metatel/core/push/push_token_model.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart' as g;
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../core/utils/device_util.dart';
import '../../../../core/utils/events_bus.dart';
import '../../../../core/values/config.dart';
import '../../models/res/base_res_model.dart';
import '../../models/res/oss_token_res_model.dart';
import '../../models/res/user_model.dart';
import '../../services/config_service.dart';
import '../native/chatio/chatio/chatio_ffi.dart';
import 'base_dio_api.dart';
import 'did.dart';
import 'other_api.dart';
import 'staking_api.dart';

 String registKey = 'p3kR5qYUJ0hWMZK7EHZLCMI4qACVVwh4';

class RegisterApiProvider extends BaseDioClient {
  static String sendSmsCodeApi = '/api/user/get_sms_code';
  static String loginApi = '/api/user/login';
  static String realNameAuthAPi = '/api/user/real_name_auth';
  static String realInfoAPi = '/api/user/info';

  Dio _createDio() {
    BaseOptions options = BaseOptions();
    options.connectTimeout = const Duration(seconds: 30);
    options.sendTimeout = const Duration(seconds: 30);
    options.contentType = 'application/json; charset=utf-8';
    options.headers = {
      HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
    };
    var dio = super.createDio(options: options);
    return dio;
  }

  /// 发送短信验证码
  Future<Response<dynamic>> sendSmsCode(String phone) async {
    var url = '${Config.registerApiUrl()}$sendSmsCodeApi';
    Map<String,dynamic> map = {'phone':phone};
    try {
      var response = await _createDio().get(url,queryParameters: map);
      AppLogger.d(
          'sendSmsCode  statusCode:${response.statusCode}   body:${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: response.data,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      return Response(
        statusCode: -100,
        statusMessage: '',
        data: null,
        requestOptions: RequestOptions(path: ''),
      );
    }
  }

  Future<Response<dynamic>> login(String phone,String code) async {
    var url = '${Config.registerApiUrl()}$loginApi';
    Map<String,dynamic> map = {'phone':phone,'code':code};
    try {
      var response = await _createDio().post(url,data: map);
      AppLogger.d(
          'login  statusCode:${response.statusCode}   body:${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: response.data,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      return Response(
        statusCode: -100,
        statusMessage: '',
        data: null,
        requestOptions: RequestOptions(path: ''),
      );
    }
  }


  Future<Response<dynamic>> realNameAuth(String phone,String name,String id) async {
    var url = '${Config.registerApiUrl()}$realNameAuthAPi';
    Map<String,dynamic> map = {'phone':phone,'name':name,'id':id};
    AppLogger.d('realNameAuth  url:${url} map=$map');
    try {
      var response = await _createDio().post(url,data: map);
      AppLogger.d(
          'realNameAuth  statusCode:${response.statusCode}   body:${response.data}');

      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: response.data,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e(
          'realNameAuth  error:${e.toString()}');
      return Response(
        statusCode: -100,
        statusMessage: '',
        data: null,
        requestOptions: RequestOptions(path: ''),
      );
    }
  }


  Future<Response<dynamic>> getRealState(String phone,) async {
    var url = '${Config.registerApiUrl()}$realInfoAPi';
    Map<String,dynamic> map = {'phone':phone};
    AppLogger.d('getRealState  url:${url} map=$map');
    try {
      var response = await _createDio().get(url,queryParameters: map);
      AppLogger.d(
          'getRealState  statusCode:${response.statusCode}   body:${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: response.data,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e(
          'getRealState  error:${e.toString()}');
      return Response(
        statusCode: -100,
        statusMessage: '',
        data: null,
        requestOptions: RequestOptions(path: ''),
      );
    }
  }
}
