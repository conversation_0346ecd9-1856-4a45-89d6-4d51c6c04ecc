// 1. 用户状态枚举
enum UserType {
  normal,
  member,
}

// 2. 用户状态数据模型
class UserStatus {
  final UserType type;
  final DateTime? membershipExpiry;
  final bool isLoggedIn;

  UserStatus({
    required this.type,
    this.membershipExpiry,
    required this.isLoggedIn,
  });

  factory UserStatus.notLoggedIn() {
    return UserStatus(
      type: UserType.normal,
      isLoggedIn: false,
    );
  }

  factory UserStatus.normalUser() {
    return UserStatus(
      type: UserType.normal,
      isLoggedIn: true,
    );
  }

  factory UserStatus.member({
    required DateTime membershipExpiry,
  }) {
    return UserStatus(
      type: UserType.member,
      membershipExpiry: membershipExpiry,
      isLoggedIn: true,
    );
  }

  bool get isMember => type == UserType.member;
  bool get isNotLoggedIn => !isLoggedIn;

  // 检查会员是否过期
  bool get isMembershipExpired {
    if (!isMember || membershipExpiry == null) return false;
    return DateTime.now().isAfter(membershipExpiry!);
  }

  // 序列化方法
  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'membershipExpiry': membershipExpiry?.toIso8601String(),
      'isLoggedIn': isLoggedIn,
    };
  }

  factory UserStatus.fromJson(Map<String, dynamic> json) {
    return UserStatus(
      type: UserType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => UserType.normal,
      ),
      membershipExpiry: json['membershipExpiry'] != null ? DateTime.parse(json['membershipExpiry']) : null,
      isLoggedIn: json['isLoggedIn'] ?? false,
    );
  }
}
