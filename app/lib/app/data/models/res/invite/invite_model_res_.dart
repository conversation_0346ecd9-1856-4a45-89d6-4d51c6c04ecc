class InviteModelRes {
  int? code;
  String? msg;
  // List<dynamic>? data;
  ExtraData? extraData;

  InviteModelRes({this.code, this.msg,});

  InviteModelRes.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    var jsonExtra = json['extra_data'];
    if(jsonExtra!=null){
      extraData=ExtraData.fromJson(jsonExtra);
    }
  }

  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['code'] = code;
    mapData['msg'] = msg;
    // mapData['data'] = data;
    return mapData;
  }
}

class ExtraData{
  String? address;
  String? node;
  ExtraData.fromJson(Map<String, dynamic> json) {
    address = json['address'];
    node = json['node'];
  }
}