class InviteInfoModelRes {
  int? code;
  String? msg;
  InviteInfoData? data;
  ExtraData? extraData;

  InviteInfoModelRes({this.code, this.msg, this.data});

  InviteInfoModelRes.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    var dataJson = json['data'];
    if(dataJson!=null){
      data=InviteInfoData.fromJson(dataJson);
    }
    var node=json['extra_data'];
    if(node!=null){
      extraData=ExtraData.fromJson(node);
    }
  }

  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['code'] = code;
    mapData['msg'] = msg;
    mapData['data'] = data;
    return mapData;
  }
}
class InviteInfoData{
  int? status; ///被邀请状态 0=无邀请人 1=有邀请人
  int? number; ///当前用户邀请新用户数量
  String? userName; ///邀请人通讯账号

  InviteInfoData.fromJson(Map<String, dynamic> json){
    status=json['be_invited_status'];
    number=json['invite_number'];
    userName=json['invite_user_name'];
  }
}
class ExtraData{
  String? node;
  ExtraData.fromJson(Map<String, dynamic> json) {
    node = json['node'];
  }
}