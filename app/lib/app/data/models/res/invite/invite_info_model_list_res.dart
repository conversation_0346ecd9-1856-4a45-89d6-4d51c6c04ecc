import '../../own_info_model.dart';

class InviteInfoListModelRes {
  int? code;
  String? msg;
  InviteInfoListData? data;

  InviteInfoListModelRes({this.code, this.msg, this.data});

  InviteInfoListModelRes.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    var dataJson = json['data'];
    if(dataJson!=null){
      data=InviteInfoListData.fromJson(dataJson);
    }
  }

  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['code'] = code;
    mapData['msg'] = msg;
    mapData['data'] = data;
    return mapData;
  }
}
class InviteInfoListData{
  int? currentPage;
  int? lastPage;
  int? total;
  List<InviteInfoListDataModel>? list;

  InviteInfoListData.fromJson(Map<String, dynamic> json){
    var jsonList = json['list'];
    if (jsonList != null) {
      list = <InviteInfoListDataModel>[];
      jsonList.forEach((v) {
        list?.add(InviteInfoListDataModel.fromJson(v));
      });
    }
    currentPage=json['current_page'];
    lastPage=json['last_page'];
    total=json['total'];
  }
}
class InviteInfoListDataModel{
  int? id;
  String? inviteUserName;
  String? invitedUserName;
  String? createTime;
  String? nickname;
  String? avatar;
  String? ioiID;
  String? userName;//拿来处理更新信息过后有@的情况

  InviteInfoListDataModel({
    this.id,
    this.inviteUserName, ///邀请人的通讯账号
    this.invitedUserName, ///被邀请人的通讯账号
    this.createTime, ///创建时间
    this.nickname,
    this.avatar,
    this.ioiID,
  });
  InviteInfoListDataModel.fromJson(Map<String, dynamic> json){
    id=json['id'];
    inviteUserName=json['invite_user_name'];
    invitedUserName=json['invited_user_name'];
    createTime=json['create_time'];
  }

  InviteInfoListDataModel.copyFromOwnInfoModelData(OwnInfoModelData data){
    nickname=data.nickname;
    avatar=data.avatar;
    ioiID=data.ioiID;
    invitedUserName=data.name;
    userName = data.userName;
  }
}