class InvitePosterModelRes {
  int? code;
  String? msg;
  PosterData? data;

  InvitePosterModelRes({this.code, this.msg, this.data});

  InvitePosterModelRes.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    var dataJson = json['data'];
    if(dataJson!=null){
      data=PosterData.fromJson(dataJson);
    }
  }

}
class PosterData{
  String? path; ///海报url
  String? inviteCode; ///邀请码 海外用
  String? discourse; ///分享链接描述

  PosterData.fromJson(Map<String, dynamic> json){
    path=json['path'];
    inviteCode=json['invite_code'];
    discourse=json['discourse'];
  }
}