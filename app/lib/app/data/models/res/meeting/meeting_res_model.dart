import 'package:flutter_metatel/app/data/models/meeting.dart';
import 'package:flutter_metatel/app/data/models/res/meeting/base_res_model.dart';

class MeetingResModel extends BaseResModel {
  List<Meeting>? data;

  MeetingResModel.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <Meeting>[];
      v.forEach((v) {
        arr0.add(Meeting.fromJson(v));
      });
      this.data = arr0;
    }
  }

  Map<String, dynamic> toJson() {
    final data = super.toJson();
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v.toJson());
      });
      data['data'] = arr0;
    }
    return data;
  }
}
