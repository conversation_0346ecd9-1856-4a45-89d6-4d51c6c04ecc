import 'package:flutter_metatel/app/data/models/pre_sign_model.dart';

import 'base_moment_res_model.dart';

class PreSignResModel extends BaseMomentResModel {
  PreSignModel? data;
  String? message;
  bool? status;
  PreSignResModel({
    this.data,
    this.message,
    this.status,
  });
  PreSignResModel.fromJson(Map<String, dynamic> json): super.fromJson(json)  {
    data = (json['data'] != null) ? PreSignModel.fromJson(json['data']) : null;
    message = json['message']?.toString();
    status = json['status'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['data'] = this.data!.toJson();
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}