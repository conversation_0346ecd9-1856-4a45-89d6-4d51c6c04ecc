import 'base_moment_res_model.dart';

class MomentInfoResModel extends BaseMomentResModel{
  MomentInfo? data;

  MomentInfoResModel.fromJson(Map<String, dynamic> json): super.fromJson(json) {
    data = (json['data'] != null) ? MomentInfo.fromJson(json['data']) : null;
  }
  Map<String, dynamic> toJson() {
    final data = super.toJson();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}


class MomentInfo {
  String? description;
  String? groupId;
  String? title;
  String? username;

  MomentInfo({
    this.description,
    this.groupId,
    this.title,
    this.username,
  });
  MomentInfo.fromJson(Map<String, dynamic> json) {
    description = json['description']?.toString();
    groupId = json['groupId']?.toString();
    title = json['title']?.toString();
    username = json['username']?.toString();
  }
  Map<String, dynamic> toJ<PERSON>() {
    final data = <String, dynamic>{};
    data['description'] = description;
    data['groupId'] = groupId;
    data['title'] = title;
    data['username'] = username;
    return data;
  }
}
