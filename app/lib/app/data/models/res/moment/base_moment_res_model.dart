class BaseMomentResModel {
  bool? status;
  String? message;
  

  BaseMomentResModel({
    this.status,
    this.message,    
  });
  BaseMomentResModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message']?.toString();    
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;    
    return data;
  }
}