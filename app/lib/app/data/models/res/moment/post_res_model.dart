import 'package:flutter_metatel/app/data/models/post.dart';
import 'package:flutter_metatel/app/data/models/res/moment/base_moment_res_model.dart';

class PostResModel extends BaseMomentResModel{
  PostPaginationModel? data;

  PostResModel.fromJson(Map<String, dynamic> json): super.fromJson(json) {
    data = (json['data'] != null) ? PostPaginationModel.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final data = super.toJson();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class PostPaginationModel {
  int currentPage = 1;  
  int rowPerPage = 10;
  int totalCount = 0;
  int totalPage = 0;
  List<Post>? posts;

  PostPaginationModel.fromJson(Map<String, dynamic> json) {
    currentPage = json['currentPage']?.toInt();
    if (json['posts'] != null) {
      final v = json['posts'];
      final arr0 = <Post>[];
      v.forEach((v) {
        arr0.add(Post.fromJson(v));
      });
      posts = arr0;
    }
    rowPerPage = json['rowPerPage']?.toInt();
    totalCount = json['totalCount']?.toInt();
    totalPage = totalCount ~/ rowPerPage;
    if(totalCount % rowPerPage > 0 ) {  // 若有余数，表明有剩余数据，添加多一页展示
      totalPage = totalPage + 1;
    }  
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['currentPage'] = currentPage;
    if (posts != null) {
      final v = posts;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v.toJson());
      });
      data['posts'] = arr0;
    }
    data['rowPerPage'] = rowPerPage;
    data['totalCount'] = totalCount;
    return data;
  }
}
