import '../../post.dart';
import 'base_moment_res_model.dart';

class CommentResModel extends BaseMomentResModel{
  CommentPaginationModel? data;

  CommentResModel.fromJson(Map<String, dynamic> json): super.fromJson(json) {
    data = (json['data'] != null) ? CommentPaginationModel.fromJson(json['data']) : null;
  }

  // Map<String, dynamic> toJson() {
  //   final data = super.toJson();
  //   if (this.data != null) {
  //     data['data'] = this.data!.toJson();
  //   }
  //   return data;
  // }
}

class CommentPaginationModel {
  int currentPage = 1;
  int rowPerPage = 10;
  int totalCount = 0;
  int totalPage = 0;
  List<Comment>? comments;

  CommentPaginationModel.init(dynamic comments) {    
    if (comments != null) {
      final v = comments;
      final arr0 = <Comment>[];
      v.forEach((v) {
        arr0.add(Comment.fromJson(v));
      });
      this.comments = arr0;
    }
  }

  CommentPaginationModel.fromJson(Map<String, dynamic> json) {    
    if (json['comments'] != null) {
      final v = json['comments'];
      final arr0 = <Comment>[];
      v.forEach((v) {
        arr0.add(Comment.fromJson(v));
      });
      comments = arr0;
    }
    currentPage = json['currentPage'];
    rowPerPage = json['rowPerPage'];
    totalCount = json['totalCount'];
    totalPage = totalCount ~/ rowPerPage;
    if(totalCount % rowPerPage > 0 ) {  // 若有余数，表明有剩余数据，添加多一页来展示
      totalPage = totalPage + 1;
    }
  }

  void updateComment({CommentPaginationModel? commentPaginationModel}) {
    currentPage = commentPaginationModel?.currentPage ?? 1;
    rowPerPage = commentPaginationModel?.rowPerPage ?? 10;
    totalCount = commentPaginationModel?.totalCount ?? 0;
    totalPage = totalCount ~/ rowPerPage;
    if(totalCount % rowPerPage > 0 ) {  // 若有余数，表明有剩余数据，添加多一页来展示
      totalPage = totalPage + 1;
    }
    if(currentPage == 1){
      comments = commentPaginationModel?.comments;
    } else {
      comments?.addAll(commentPaginationModel?.comments ?? []);
    }   
  }
}