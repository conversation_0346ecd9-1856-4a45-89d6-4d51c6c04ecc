import 'base_moment_res_model.dart';

class UploadImgResModel extends BaseMomentResModel{
  UploadedImage? data;

  UploadImgResModel.fromJson(Map<String, dynamic> json): super.fromJson(json) {
    data = (json['data'] != null) ? UploadedImage.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final data = super.toJson();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class UploadedImage {
  String? contentType;
  String? filename;
  int? size;
  String? url;

  UploadedImage({
    this.contentType,
    this.filename,
    this.size,
    this.url,
  });
  UploadedImage.fromJson(Map<String, dynamic> json) {
    contentType = json['contentType']?.toString();
    filename = json['filename']?.toString();
    size = json['size']?.toInt();
    url = json['url']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['contentType'] = contentType;
    data['filename'] = filename;
    data['size'] = size;
    data['url'] = url;
    return data;
  }

}