import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// code : 200
/// message : "OK"
/// data : [{"taskid":1,"tasktext":"注册账号","etasktext":"Sign up","state":true,"reward":20},{"taskid":2,"tasktext":"修改头像","etasktext":"Set Profile Photo","state":true,"reward":20},{"taskid":3,"tasktext":"修改昵称","etasktext":"Edit name","state":true,"reward":20},{"taskid":4,"tasktext":"第一个好友","etasktext":"Add a contact","state":true,"reward":20},{"taskid":5,"tasktext":"第一个社区","etasktext":"Join a group","state":true,"reward":20},{"taskid":6,"tasktext":"第一条点对点消息","etasktext":"send a massge to contact","state":false,"reward":0},{"taskid":7,"tasktext":"第一条群聊消息","etasktext":"send a massge to group","state":false,"reward":0},{"taskid":8,"tasktext":"第一个语音通话","etasktext":"Make a voice call.","state":false,"reward":0},{"taskid":9,"tasktext":"第一个视频通话","etasktext":"Make a video call.","state":false,"reward":0},{"taskid":10,"tasktext":"第一个Dapp","etasktext":"Authorize login to a DAPP","state":false,"reward":0}]

class MiningTask extends BaseRes {
  MiningTask({
    this.data,
  });

  MiningTask.fromJson(dynamic json) {
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(MiningTaskData.fromJson(v));
      });
    }
  }

  List<MiningTaskData>? data;

  @override
  Map<String, dynamic> toJson() {
    final map = super.toJson();
    if (data != null) {
      map['data'] = data?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// taskid : 1
/// tasktext : "注册账号"
/// etasktext : "Sign up"
/// state : true
/// reward : 20

class MiningTaskData extends BaseRes {
  MiningTaskData({
    this.taskid,
    this.tasktext,
    this.etasktext,
    this.state,
    this.reward,
    this.value,
  });

  MiningTaskData.fromJson(dynamic json) : super.fromJson(json) {
    taskid = json['taskid'];
    tasktext = json['tasktext'];
    etasktext = json['etasktext'];
    state = json['state'];
    reward = json['reward'];
    value = json['value'];
  }

  int? taskid;
  String? tasktext;
  String? etasktext;
  bool? state;
  int? reward;
  int? value;

  @override
  Map<String, dynamic> toJson() {
    final map = super.toJson();
    map['taskid'] = taskid;
    map['tasktext'] = tasktext;
    map['etasktext'] = etasktext;
    map['state'] = state;
    map['reward'] = reward;
    map['value'] = value;
    return map;
  }
}
