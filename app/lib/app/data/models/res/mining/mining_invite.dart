import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"total":300,"level1":200,"level2":100,"size":100,"page":1,"value":[{"account":"8TzZudAZ4CkfbZ3NA5WfXkve9t6SXrpt3NzRUZavnMgs","time":"2023-3-18 14:00:12","reward":100},{"account":"9TzZudAZ4CkfbZ3NA5WfXkve9t6SXrpt3NzRUZavnMgs","time":"2023-3-19 14:00:12","reward":100}]}

class MiningInvite extends BaseRes {
  MiningInvite({
    this.data,
  });

  MiningInvite.fromJson(dynamic json) {
    data =
        json['data'] != null ? MiningInviteData.fromJson(json['data']) : null;
  }

  MiningInviteData? data;

  @override
  Map<String, dynamic> to<PERSON><PERSON>() {
    final map = super.toJson();
    if (data != null) {
      map['data'] = data?.toJson();
    }
    return map;
  }
}

/// total : 300
/// level1 : 200
/// level2 : 100
/// size : 100
/// page : 1
/// value : [{"account":"8TzZudAZ4CkfbZ3NA5WfXkve9t6SXrpt3NzRUZavnMgs","time":"2023-3-18 14:00:12","reward":100},{"account":"9TzZudAZ4CkfbZ3NA5WfXkve9t6SXrpt3NzRUZavnMgs","time":"2023-3-19 14:00:12","reward":100}]

class MiningInviteData {
  MiningInviteData({
    this.total,
    this.level1,
    this.level2,
    this.size,
    this.page,
    this.value,
    this.totalPage,
  });

  MiningInviteData.fromJson(dynamic json) {
    total = json['total'];
    level1 = json['level1'];
    level2 = json['level2'];
    size = json['size'];
    page = json['page'];
    totalPage = json['totalpage'];
    if (json['value'] != null) {
      value = [];
      json['value'].forEach((v) {
        value?.add(MiningInviteValue.fromJson(v));
      });
    }
  }

  int? total;
  int? level1;
  int? level2;
  int? size;
  int? page;
  int? totalPage;
  List<MiningInviteValue>? value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['total'] = total;
    map['level1'] = level1;
    map['level2'] = level2;
    map['size'] = size;
    map['page'] = page;
    map['totalpage'] = totalPage;
    if (value != null) {
      map['value'] = value?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// account : "8TzZudAZ4CkfbZ3NA5WfXkve9t6SXrpt3NzRUZavnMgs"
/// time : "2023-3-18 14:00:12"
/// reward : 100

class MiningInviteValue extends BaseRes {
  MiningInviteValue({
    this.account,
    this.time,
    this.reward,
  });

  MiningInviteValue.fromJson(dynamic json) : super.fromJson(json) {
    account = json['account'];
    time = json['time'];
    reward = json['reward'];
  }

  String? account;
  String? time;
  int? reward;

  @override
  Map<String, dynamic> toJson() {
    final map = super.toJson();
    map['account'] = account;
    map['time'] = time;
    map['reward'] = reward;
    return map;
  }
}
