import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"info":[{"value":"0.0","number":0,"class":"C"},{"value":"0.0","number":0,"class":"B"},{"value":"0.0","number":0,"class":"A"},{"value":"0.0","number":0,"class":"CITYNODE"},{"value":"0.0","number":0,"class":"CREATENODE"}],"total":0}

class MiningInviteLevel extends BaseRes{
  MiningInviteLevel({
    this.data,
  });

  MiningInviteLevel.fromJson(dynamic json) {
    data = json['data'] != null ? MiningInviteLevelData.fromJson(json['data']) : null;
  }

  MiningInviteLevelData? data;
}

/// info : [{"value":"0.0","number":0,"class":"C"},{"value":"0.0","number":0,"class":"B"},{"value":"0.0","number":0,"class":"A"},{"value":"0.0","number":0,"class":"CITYNODE"},{"value":"0.0","number":0,"class":"CREATENODE"}]
/// total : 0

class MiningInviteLevelData {
  MiningInviteLevelData({
    this.info,
    this.total,
    this.stage,
  });

  MiningInviteLevelData.fromJson(dynamic json) {
    if (json['info'] != null) {
      info = [];
      json['info'].forEach((v) {
        info?.add(Info.fromJson(v));
      });
    }
    total = json['total'];
    stage = json['stage'];
  }

  List<Info>? info;
  double? total;
  int? stage;

}

/// value : "0.0"
/// number : 0
/// class : "C"

class Info {
  Info({
    this.value,
    this.number,
    this.type,
  });

  Info.fromJson(dynamic json) {
    String value = json['value'];
    this.value=double.parse(value);
    number = json['number'];
    type = json['class'];
  }

  double? value;
  int? number;
  String? type;
}
