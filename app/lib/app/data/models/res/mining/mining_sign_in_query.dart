import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"value":[{"datatimes":"2023-06-13 00:56:11"},{"datatimes":"2023-06-12 20:55:28"}],"days":2}

class MiningSignInQuery extends BaseRes{
  MiningSignInQuery({
      this.data,});

  MiningSignInQuery.fromJson(dynamic json):super.fromJson(json){
    data = json['data'] != null ? MiningSignData.fromJson(json['data']) : null;
  }
  MiningSignData? data;
}

/// value : [{"datatimes":"2023-06-13 00:56:11"},{"datatimes":"2023-06-12 20:55:28"}]
/// days : 2

class MiningSignData {
  MiningSignData({
      this.value, 
      this.days,});

  MiningSignData.fromJson(dynamic json) {
    if (json['value'] != null) {
      value = [];
      json['value'].forEach((v) {
        value?.add(SignDate.fromJson(v));
      });
    }
    days = json['days'];
  }
  List<SignDate>? value;
  int? days;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (value != null) {
      map['value'] = value?.map((v) => v.toJson()).toList();
    }
    map['days'] = days;
    return map;
  }

}

/// datatimes : "2023-06-13 00:56:11"

class SignDate {
  SignDate({
      this.datatimes,});

  SignDate.fromJson(dynamic json) {
    datatimes = json['datatimes'];
  }
  String? datatimes;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['datatimes'] = datatimes;
    return map;
  }

}