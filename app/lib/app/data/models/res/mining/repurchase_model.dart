//FileName repurchase_model
// <AUTHOR>
//@Date 2024/3/11 12:15
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

//申请兑换
class AddSwapInfo extends BaseRes {
  AddSwapInfo({
    this.data,
  });

  AddSwapInfo.fromJson(dynamic json) : super.fromJson(json) {
    data = json['data'] != null ? AddSwapData.fromJson(json['data']) : null;
  }

  AddSwapData? data;
}

class AddSwapData {
  int? result;

  AddSwapData(this.result);

  AddSwapData.fromJson(dynamic json) {
    result = json['result'];
  }
}

//查询申请兑换的额度
class GetSwapAmountInfo extends BaseRes {
  GetSwapAmountInfo({
    this.data,
  });

  GetSwapAmountInfo.fromJson(dynamic json) : super.fromJson(json) {
    data =
        json['data'] != null ? GetSwapAmountData.fromJson(json['data']) : null;
  }

  GetSwapAmountData? data;
}

class GetSwapAmountData {
  int? tidNumber;
  double? maxSwapValue;
  int? stage;
  double? vlsOfTid;
  double? result;
  int? swapValue;
  GetSwapAmountData(this.result,this.swapValue);

  GetSwapAmountData.fromJson(dynamic json) {
    if (json['result'] is int) {
      int a = json['result'];
      result = a / 1;
    } else {
      result = json['result'];
    }
    if (json['maxSwapValue'] is int) {
      int a = json['maxSwapValue'];
      maxSwapValue = a / 1;
    } else {
      maxSwapValue = json['maxSwapValue'];
    }
    if (json['vlsOfTid'] is int) {
      int a = json['vlsOfTid'];
      vlsOfTid = a / 1;
    } else {
      vlsOfTid = json['vlsOfTid'];
    }
    swapValue = json['swapValue'];
    stage = json['stage'];
    tidNumber = json['TidNumber'];
  }
}

//查询兑换记录
class GetSwapInfo extends BaseRes {
  GetSwapInfo({
    this.data,
  }) ;

  GetSwapInfo.fromJson(dynamic json) : super.fromJson(json) {
    data = json['data'] != null ? GetSwapInfoData.fromJson(json['data']) : null;
  }

  GetSwapInfoData? data;
}

class GetSwapInfoData {
  List<GetSwapInfoDataRes>? result;
  int? currentPage;
  int? pageSize;
  int? pageTotal;
  GetSwapInfoData(this.result,this.currentPage,this.pageSize,this.pageTotal);

  GetSwapInfoData.fromJson(dynamic json) {
    pageSize = json['page_size'];
    pageTotal = json['page_total'];
    currentPage = json['current_page'];
    if (json['result'] != null) {
      result = [];
      json['result'].forEach((v) {
        result?.add(GetSwapInfoDataRes.fromJson(v));
      });
    }
  }
}

class GetSwapInfoDataRes {
  String? account;
  String? address;
  double? amount;
  double? prize;
  String? createtime;
  String? allowtime;
  int? id;
  int? status;
  String? txhash;
  int? swapvalue;
  GetSwapInfoDataRes(
    this.account,
    this.address,
    this.amount,
    this.createtime,
    this.id,
    this.status,
    this.txhash,
    this.allowtime,
    this.prize,
    this.swapvalue
  );

  GetSwapInfoDataRes.fromJson(dynamic json) {
    account = json['account'];
    address = json['address'];
    if (json['amount'] is int) {
      int a = json['amount'];
      amount = a / 1;
    } else {
      amount = json['amount'];
    }
    if (json['prize'] is int) {
      int a = json['prize'];
      prize = a / 1;
    } else {
      prize = json['prize'];
    }
    createtime = json['createtime'];
    id = json['id'];
    status = json['status'];
    txhash = json['txhash'];
    swapvalue = json['swapvalue'];
    allowtime = json['allowtime'];
  }
}
