import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"chatvl":96.8,"invitevl":100,"taskvl":20,"sumall":200}

class MiningTotal extends BaseRes{
  MiningTotal({
      this.data,});

  MiningTotal.fromJson(dynamic json) :super.fromJson(json){
    data = json['data'] != null ? MiningTotalData.fromJson(json['data']) : null;
  }
  MiningTotalData? data;

  @override
  Map<String, dynamic> toJson() {
    final map = super.toJson();
    if (data != null) {
      map['data'] = data?.toJson();
    }
    return map;
  }

}

/// chatvl : 96.8
/// invitevl : 100
/// taskvl : 20
/// sumall : 200

class MiningTotalData {
  MiningTotalData({
      this.chatvl, 
      this.invitevl, 
      this.taskvl, 
      this.sumall,
      this.signin,
      this.vesting,
      this.stage});

  MiningTotalData.fromJson(dynamic json) {
    chatvl = json['chatvl'];
    invitevl = json['invitevl'];
    taskvl = json['taskvl'];
    sumall = json['sumall'];
    stage = json['stage'];
    signin = json['signin'];
    vesting = json['vesting'];
    task11 = json['task11'];
  }
  double? chatvl;
  int? invitevl;
  int? taskvl;
  int? signin;
  double? sumall;
  double? vesting;
  double? task11;
  int? stage;
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['chatvl'] = chatvl;
    map['invitevl'] = invitevl;
    map['taskvl'] = taskvl;
    map['sumall'] = sumall;
    map['stage'] = stage;
    map['task11'] = task11;
    return map;
  }

}