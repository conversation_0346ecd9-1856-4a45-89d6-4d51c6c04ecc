import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"total":2,"size":10,"page":1,"value":[{"stage":"2","stagetime":["2023-3-21","2023-3-27"],"stagevalue":"20000","stageamount":"380000","total":"33344444444380000","reward":"5678.78909"},{"stage":"1","stagetime":["2023-3-14","2023-3-20"],"stagevalue":"20000","stageamount":"380000","total":"33344444444380000","reward":"5678.78909"}]}

class MiningChatMore extends BaseRes {
  MiningChatMore({
    this.data,
  });

  MiningChatMore.fromJson(dynamic json) : super.fromJson(json) {
    data =
        json['data'] != null ? MiningChatMoreData.fromJson(json['data']) : null;
  }

  MiningChatMoreData? data;

  @override
  Map<String, dynamic> toJson() {
    final map = super.toJson();
    if (data != null) {
      map['data'] = data?.toJson();
    }
    return map;
  }
}

/// total : 2
/// size : 10
/// page : 1
/// value : [{"stage":"2","stagetime":["2023-3-21","2023-3-27"],"stagevalue":"20000","stageamount":"380000","total":"33344444444380000","reward":"5678.78909"},{"stage":"1","stagetime":["2023-3-14","2023-3-20"],"stagevalue":"20000","stageamount":"380000","total":"33344444444380000","reward":"5678.78909"}]

class MiningChatMoreData {
  MiningChatMoreData({
    this.total,
    this.size,
    this.page,
    this.value,
  });

  MiningChatMoreData.fromJson(dynamic json) {
    total = json['total'];
    size = json['size'];
    page = json['page'];
    if (json['value'] != null) {
      value = [];
      json['value'].forEach((v) {
        value?.add(MiningChatMoreValue.fromJson(v));
      });
    }
  }

  int? total;
  int? size;
  int? page;
  List<MiningChatMoreValue>? value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['total'] = total;
    map['size'] = size;
    map['page'] = page;
    if (value != null) {
      map['value'] = value?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// stage : "2"
/// stagetime : ["2023-3-21","2023-3-27"]
/// stagevalue : "20000"
/// stageamount : "380000"
/// total : "33344444444380000"
/// reward : "5678.78909"

class MiningChatMoreValue {
  MiningChatMoreValue({
    this.stage,
    this.stagetime,
    this.stagevalue,
    this.stageamount,
    this.total,
    this.reward,
  });

  MiningChatMoreValue.fromJson(dynamic json) {
    stage = json['stage'];
    stagetime =
        json['stagetime'] != null ? json['stagetime'].cast<String>() : [];
    stagevalue = json['stagevalue'];
    stageamount = json['stageamount'];
    total = json['total'];
    reward = json['reward'];
  }

  String? stage;
  List<String>? stagetime;
  String? stagevalue;
  String? stageamount;
  String? total;
  String? reward;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['stage'] = stage;
    map['stagetime'] = stagetime;
    map['stagevalue'] = stagevalue;
    map['stageamount'] = stageamount;
    map['total'] = total;
    map['reward'] = reward;
    return map;
  }
}
