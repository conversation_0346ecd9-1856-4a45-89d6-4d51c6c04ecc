import '../base_res_model.dart';

/// data : {"stage":3,"integral":5534460,"value":[{"day":"2023-3-27","detail":[{"time":1,"value":123450},{"time":0,"value":456780},{"time":23,"value":131445}]},{"day":"2023-3-28","detail":[{"time":0,"value":123450},{"time":12,"value":456780},{"time":23,"value":131445}]}]}

class MiningChat extends BaseRes {
  MiningChat({
    this.data,
  });

  MiningChat.fromJson(dynamic json) {
    data = json['data'] != null ? MiningChatData.fromJson(json['data']) : null;
  }

  MiningChatData? data;

  @override
  Map<String, dynamic> toJson() {
    final map = super.toJson();
    if (data != null) {
      map['data'] = data?.to<PERSON><PERSON>();
    }
    return map;
  }
}

/// stage : 3
/// integral : 5534460
/// value : [{"day":"2023-3-27","detail":[{"time":1,"value":123450},{"time":0,"value":456780},{"time":23,"value":131445}]},{"day":"2023-3-28","detail":[{"time":0,"value":123450},{"time":12,"value":456780},{"time":23,"value":131445}]}]

class MiningChatData {
  MiningChatData({
    this.stage,
    this.integral,
    this.value,
    this.signin,
    this.task11,
  });

  MiningChatData.fromJson(dynamic json){
    stage = json['stage'];
    integral = json['integral'];
    previous = json['previous'];
    signin = json['signin'];
    task11 = json['task11'];
    if (json['value'] != null) {
      value = [];
      json['value'].forEach((v) {
        value?.add(MiningChatValue.fromJson(v));
      });
    }
  }

  int? stage;
  double? integral;
  double? task11;
  int? previous;
  int? signin;
  List<MiningChatValue>? value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['stage'] = stage;
    map['integral'] = integral;
    map['previous'] = previous;
    map['task11'] = task11;
    if (value != null) {
      map['value'] = value?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// day : "2023-3-27"
/// detail : [{"time":1,"value":123450},{"time":0,"value":456780},{"time":23,"value":131445}]

class MiningChatValue {
  MiningChatValue({
    this.day,
    this.detail,
  });

  MiningChatValue.fromJson(dynamic json) {
    day = json['day'];
    if (json['detail'] != null) {
      detail = [];
      json['detail'].forEach((v) {
        detail?.add(MiningChatDetail.fromJson(v));
      });
    }
  }

  String? day;
  List<MiningChatDetail>? detail;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['day'] = day;
    if (detail != null) {
      map['detail'] = detail?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// time : 1
/// value : 123450

class MiningChatDetail {
  MiningChatDetail({
    this.time,
    this.value,
  });

  MiningChatDetail.fromJson(dynamic json) {
    time = json['time'];
    value = json['value'];
  }

  int? time;
  double? value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['time'] = time;
    map['value'] = value;
    return map;
  }
}
