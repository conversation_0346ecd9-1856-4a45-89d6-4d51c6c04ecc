
class SearchSbtDidInfo {
  List<SearchSbtBean>? data;
  int? code;
  String? msg;

  SearchSbtDidInfo({this.code, this.msg, this.data});

  SearchSbtDidInfo.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <SearchSbtBean>[];
      json['data'].forEach((v) {
        data?.add(SearchSbtBean.fromJson(v));
      });
    }
    code = json['code'];
    msg = json['msg'];
  }

  @override
  Map<String, dynamic> toJson() {
    final body = <String, dynamic>{};
    if (data != null) {
      body['data'] = data?.map((v) => v.toJson()).toList();
    }
    body['code'] = code;
    body['msg'] = msg;
    return body;
  }
}

class SearchSbtBean {
  String? account;
  String? address;
  SearchSbtBean({this.address,this.account});

  SearchSbtBean.fromJson(Map<String, dynamic> json) {
    address = json['address'];
    account = json['account'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['address'] = address;
    data['account'] = account;
    return data;
  }
}
