
class CreateInfo {
  Info? data;
  int? code;
  String? msg;

  CreateInfo({this.code, this.msg, this.data});

  CreateInfo.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Info?.fromJson(json['data']) : null;
    code = json['code'];
    msg = json['msg'];
  }

  @override
  Map<String, dynamic> toJson() {
    final body = <String, dynamic>{};
    body['data'] = data?.toJson();
    body['code'] = code;
    body['msg'] = msg;
    return body;
  }
}

class Info {
  int? result;
  String? txhash;

  Info({this.result,this.txhash});

  Info.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    txhash = json['txhash'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['result'] = result;
    data['txhash'] = txhash;
    return data;
  }
}
