class SearchDidInfo {
  List<SearchBean>? data;
  int? code;
  String? msg;

  SearchDidInfo({this.code, this.msg, this.data});

  SearchDidInfo.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <SearchBean>[];
      json['data'].forEach((v) {
        data?.add(SearchBean.fromJson(v));
      });
    }
    code = json['code'];
    msg = json['msg'];
  }

  @override
  Map<String, dynamic> toJson() {
    final body = <String, dynamic>{};
    if (data != null) {
      body['data'] = data?.map((v) => v.toJson()).toList();
    }
    body['code'] = code;
    body['msg'] = msg;
    return body;
  }
}

class SearchBean {
  String? did;
  String? sbt;
  int? status;
  int? powerUp;
  String? endTimes;
  SearchBean({this.did, this.sbt, this.status, this.powerUp});

  SearchBean.fromJson(Map<String, dynamic> json) {
    did = json['did'];
    sbt = json['sbt'];
    status = json['status'];
    powerUp = json['power_up'];
    endTimes = json['endtimes'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['did'] = did;
    data['sbt'] = sbt;
    data['status'] = status;
    data['power_up'] = powerUp;
    data['endtimes'] = endTimes;
    return data;
  }
}
