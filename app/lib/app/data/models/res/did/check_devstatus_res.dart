
class CheckDevstatusRes {
  bool? data;
  int? code;
  String? msg;
  int? type;//1 领取3trp,2不能领取，都没有3T了

  CheckDevstatusRes({this.code, this.msg, this.data});

  CheckDevstatusRes.fromJson(Map<String, dynamic> json) {
    data = json['data'];
    code = json['code'];
    msg = json['msg'];
    type = json['type'];
  }

  @override
  Map<String, dynamic> toJson() {
    final body = <String, dynamic>{};
    body['data'] = data;
    body['code'] = code;
    body['msg'] = msg;
    body['type'] = type;
    return body;
  }
}

