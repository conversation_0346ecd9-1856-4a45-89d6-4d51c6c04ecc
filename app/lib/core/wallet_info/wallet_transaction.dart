

class WalletTransactionModel {
  int? chainType;
  int? chainId;
  String? address;
  ///发送方钱包地址
  String? addressSend;
  String? symbol;
  String? netName;
  String? browserUrl;
  double? value;
  String? transactionHash;
  int? transactionType;
  String? memo;
  int? billType;
  String? robotUsername;

  WalletTransactionModel({
    this.chainType,
    this.chainId,
    this.address,
    this.symbol,
    this.netName,
    this.browserUrl,
    this.value,
    this.transactionHash,
    this.transactionType,
    this.memo,
    this.billType,
    this.addressSend,
    this.robotUsername,
  });

  WalletTransactionModel.fromJson(Map<String, dynamic> json){
    chainType=json['chainType'];
    chainId=json['chainId'];
    address=json['address'];
    symbol=json['symbol'];
    netName=json['netName'];
    browserUrl=json['browserUrl'];
    value=json['value'];
    transactionHash=json['transactionHash'];
    transactionType=json['transactionType'];
    memo=json['memo'];
    billType=json['billType'];
    addressSend=json['addressSend'];
    robotUsername=json['robotUsername'];
  }
  toJson(){
    Map<String,dynamic> map={
      'chainType':chainType,
      'chainId':chainId,
      'address':address,
      'symbol':symbol,
      'netName':netName,
      'browserUrl':browserUrl,
      'value':value,
      'transactionHash':transactionHash,
      'transactionType':transactionType,
      'memo':memo,
      'billType':billType,
      'addressSend':addressSend,
      'robotUsername':robotUsername,
    };
    return map;
  }
}

