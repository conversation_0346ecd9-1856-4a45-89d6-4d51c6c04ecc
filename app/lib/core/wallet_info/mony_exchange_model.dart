

import 'package:decimal/decimal.dart';

class MoneyExchangeModel {
  int? code;
  String? msg;
  int? type; ///1买 2卖
  int? stop; ///时间
  double? descTokenAmount; ///兑换数量
  String?  total; ///总价
  String?  uniPrice; ///单价
  int?  pairid; ///配对编号
  String?  tokenType; ///币种类型
  String?  url; ///分享地址

  MoneyExchangeModel({
    this.type,
    this.stop,
    this.descTokenAmount,
    this.total,
    this.uniPrice,
    this.pairid,
    this.tokenType,
    this.url,
  });

  MoneyExchangeModel.fromJson(Map<String, dynamic> json){
    code=json['code'];
    msg=json['msg'];
    type=json['type'];
    stop=json['stop'];
    var descTokenAmount=json['desc_token_amount'];
    if(descTokenAmount.runtimeType==int){
      this.descTokenAmount=(descTokenAmount as int).toDouble();
    }else if(descTokenAmount.runtimeType==double){
      this.descTokenAmount=descTokenAmount as double;
    }
    var total=json['total'];
    if(total.runtimeType==String){
      this.total=total;
    }else if(total.runtimeType==double||total.runtimeType==int){
      this.total=Decimal.parse("${total??0}").toString();
    }
    var uniPrice=json['uniPrice'];
    if(uniPrice.runtimeType==String){
      this.uniPrice=uniPrice;
    }else if(uniPrice.runtimeType==double||uniPrice.runtimeType==int){
      this.uniPrice=Decimal.parse("${uniPrice??0}").toString();
    }
    pairid=json['pairid'];
    tokenType=json['tokenType'];
    url=json['url'];
  }
}

