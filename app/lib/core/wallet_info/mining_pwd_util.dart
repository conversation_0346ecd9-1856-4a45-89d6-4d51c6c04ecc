//FileName mining_pwd_util
// <AUTHOR>
//@Date 2023/3/29 11:51

import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter_curve25519/flutter_curve25519.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/wallet_info/formatting.dart';
import 'package:get/get.dart';

import '../../../core/utils/app_log.dart';
import '../../../core/utils/pwd_util.dart';
import '../../../core/values/config.dart';

class MiningPwdUtil {
  static const tag = 'MiningPwdUtil';

  static Future<String?> encryptForData(String data,{String? rand}) async {
    var privateKey = await Get.find<AppConfigService>().getPrivateKey();
    var private = base64.decode(privateKey);
    var newKey = Curve25519.sharedKey(
        private, Uint8List.fromList(hexToBytes(rand??Config.random)));
    if (newKey.length != 32) {
      AppLogger.e('$tag encryptForData error=');
      return null;
    }
    var pwd = newKey;
    // AppLogger.d('encryptForData newKey ${toHex(newKey)}');
    var rad = Uint8List.sublistView(newKey, 16, 32);
    AppLogger.d('$tag encryptForData data=${data}');
    String body = PwdUtil.encryptForData(pwd, rad, data);
    // String decode = PwdUtil.decryptForData(pwd, rad, body);
    // AppLogger.d('$tag encryptForData decode=${decode}');
    return body;
  }

  static Future<String?> decryptForData(String data,{String? rand}) async {
    AppLogger.d('$tag decryptForData data=$data');
    // var info =String.fromCharCodes(Uint8List.fromList(base64.decode(data)));
    // AppLogger.d('$tag proBeMapList info=$info');
    AppLogger.d('decryptForData getPrivateKey');
    var privateKey = await Get.find<AppConfigService>().getPrivateKey();
    var private = base64.decode(privateKey);
    var newKey = Curve25519.sharedKey(
        private, Uint8List.fromList(hexToBytes(rand??Config.random)));
    if (newKey.length != 32) {
      AppLogger.e('$tag decryptForData error newKey.length not 32');
      return null;
    }
    var pwd = newKey;
    var rad = Uint8List.sublistView(newKey, 16, 32);
    String decode=PwdUtil.decryptForData(pwd, rad, data);
    AppLogger.d('$tag decryptForData decode2222=${decode}');
    return decode;
  }
  static Future<String?> decryptForDataByKey(String data,String key) async {
    AppLogger.d('$tag decryptForData data=$data');
    // var info =String.fromCharCodes(Uint8List.fromList(base64.decode(data)));
    // AppLogger.d('$tag proBeMapList info=$info');
    var privateKey = key;
    var private = Uint8List.fromList(privateKey.codeUnits);//base64.decode(privateKey);
    AppLogger.d('decryptForData private=$private');

    // var newKey = Curve25519.sharedKey(
    //     private, Uint8List.fromList(hexToBytes(rand??Config.random)));
    if (private.length != 32) {
      AppLogger.e('$tag decryptForData error newKey.length not 32');
      return null;
    }
    // var pwd = newKey;
    var rad = Uint8List.sublistView(private, 16, 32);
    String decode=PwdUtil.decryptForData(private, rad, data);
    AppLogger.d('$tag decryptForData decode2222=${decode}');
    return decode;
  }
}
