class WalletModel {
  WalletModel({
    this.address,
    this.gasModel,
    this.name,
    this.balance,
    this.gas,
    this.symbol,
    this.picture,
    this.privateKey,
    this.type,
    this.to,
    this.value,
    this.chainType,
    this.transactionType,
    this.resource,
    this.functionSelector,
    this.chainId,
    this.contractAdrress,
    this.isSelected,
    this.maxGas,
    this.minGas,
    this.keyboxId,
    this.walletType,
    this.cardId,
    this.paymentType,
    this.index_,
  });
  String? address;
  String? name;
  double? balance;
  double? gas;
  String? symbol;
  String? picture;
  String? privateKey;
  String? value;
  String? gasSymbol;
  int? type;
  int? chainType;
  String? to;
  String? contractAdrress;

  String? transactionType;
  String? resource;
  String? functionSelector;
  int? chainId;
  bool? isSelected;
  double? maxGas;
  double? minGas;
  int? bandwidth;//带宽
  int? walletType;
  String? keyboxId;
  String? cardId;
  int? paymentType;
  int? index_;
  GasModel? gasModel;
  WalletModel.copy(WalletModel model) {
    name = model.name;
    address = model.address;
    balance = model.balance;
    gas = model.gas;
    symbol = model.symbol;
    picture = model.picture;
    privateKey = model.privateKey;
    type = model.type;
    value= model.value;
    to = model.to;
    chainType = model.chainType;
    gasSymbol = model.gasSymbol;
    maxGas = model.maxGas;
    minGas = model.minGas;
    bandwidth = model.bandwidth;
    paymentType = model.paymentType;
    index_ = model.index_;
    gasModel = model.gasModel;
  }
  Map<String,dynamic> toJson(){
    Map<String,dynamic> m={
      "address":address,
      "chainType":chainType,
      "chainId":chainId,
    };
    return m;
  }

  WalletModel.fromJson(Map<String,dynamic> json){
    address = json['address'];
    chainType = json['chainType'];
    chainId = json['chainId'];
  }
}

class TrxTransactionType {
  static const triggerSmartContract = 'TriggerSmartContract';
  static const transferContract = 'TransferContract';
  static const freezeBalanceV2Contract = 'FreezeBalanceV2Contract';
  static const unfreezeBalanceV2Contract = 'UnfreezeBalanceV2Contract';
  static const transferAssetContract = 'TransferAssetContract';
  static const voteWitnessContract = 'VoteWitnessContract';
}

class EthTransactionType {
  static const transfer = 'transfer';//token转账
  static const transferContract = 'TransferContract';//主币转账
  static const otherContract = 'otherContract';//其他合约
  static const approveContract = 'approveContract';//approve

}
class ResourceType {
  static const energy = 'ENERGY';
  static const bandwidth = 'BANDWIDTH';
}
class GasModel {
  num? gasLimit;
  num? gasPrice;
  num? multiple;//gas倍数
  GasModel({this.gasLimit,this.gasPrice,this.multiple = 1.0});
  Map<String,dynamic> toJson(){
    Map<String,dynamic> m={
      "gasLimit":gasLimit,
      "gasPrice":gasPrice,
      "multiple":multiple,
    };
    return m;
  }

  GasModel.fromJson(Map<String,dynamic> json){
    gasLimit = json['gasLimit'];
    gasPrice = json['gasPrice'];
    multiple = json['multiple'];
  }
}
