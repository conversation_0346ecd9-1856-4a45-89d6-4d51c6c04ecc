import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_browser/flutter_browser.dart';

class BrowserPage extends StatefulWidget{
  const BrowserPage({
    Key? key,
    this.homeUrl,
    this.searchEngineModel,
    this.debug = false,
    this.isHome,
    this.title,
    this.openUniApp,
    this.setOrientations = const [
      DeviceOrientation.portraitUp,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ],
    this.defaultOrientationsPortrait = const [
      DeviceOrientation.portraitUp,
    ],
  }) : super(key: key);
  final String? homeUrl;
  final SearchEngineModel? searchEngineModel;
  final bool debug;
  final bool? isHome;
  final String? title;
  final ValueChanged<dynamic>? openUniApp;
  final List<DeviceOrientation> setOrientations;
  final List<DeviceOrientation> defaultOrientationsPortrait;
  static List<DeviceOrientation> staticSetOrientations=const [
    DeviceOrientation.portraitUp,
  ];

  @override
  State<StatefulWidget> createState() => _BrowserPageState();
}

class _BrowserPageState extends State<BrowserPage> {

  @override
  void dispose() {
    BrowserPage.staticSetOrientations=widget.defaultOrientationsPortrait;
    SystemChrome.setPreferredOrientations(widget.defaultOrientationsPortrait);
    super.dispose();
  }

  @override
  void initState() {
    BrowserPage.staticSetOrientations=widget.setOrientations;
    SystemChrome.setPreferredOrientations(widget.setOrientations);
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: BrowserWidget(
        homeUrl: widget.homeUrl,
        searchEngineModel: widget.searchEngineModel,
        debug: widget.debug,
        isHome:widget.isHome,
        title: widget.title,
        openUniApp: widget.openUniApp,
        isPackage: true,
      ),
    );
  }
}
