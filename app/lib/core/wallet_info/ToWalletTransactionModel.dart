
class ToWalletTransactionModel {
  ToWalletTransactionModel({
    this.toAddress,
    this.title,
    this.content,
    this.amount,
    this.memo,
    this.transactionType,
    this.billType,
    this.chainList,
    this.chainType,
    this.robotUsername,
  });

  ToWalletTransactionModel.fromJson(dynamic json) {
    toAddress = json['toAddress'];
    title = json['title'];
    content = json['content'];
    num amount = json['amount'];
    this.amount=amount.toDouble();
    memo = json['memo'];
    transactionType = json['transactionType'];
    billType = json['billType'];
    chainType = json['chainType'];
    robotUsername = json['robotUsername'];
    if (json['chain_list'] != null) {
      chainList = [];
      json['chain_list'].forEach((v) {
        chainList?.add(ChainList.fromJson(v));
      });
    }
  }
  String? toAddress;
  String? title;
  String? content;
  double? amount;
  String? memo;
  int? transactionType;
  int? billType;
  int? chainType;
  String? robotUsername;
  List<ChainList>? chainList;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['toAddress'] = toAddress;
    map['title'] = title;
    map['content'] = content;
    map['amount'] = amount;
    map['memo'] = memo;
    map['transactionType'] = transactionType;
    map['billType'] = billType;
    map['chainType'] = chainType;
    map['robotUsername'] = robotUsername;
    if (chainList != null) {
      map['chain_list'] = chainList?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class ChainList {
  ChainList({
    this.chainId,
    this.netName,
    this.browserUrl,
    this.tokenSymbol,
    this.tokenAddress,
    this.chainSymbol,});

  ChainList.fromJson(dynamic json) {
    chainId = json['chainId'];
    netName = json['netName'];
    browserUrl = json['browserUrl'];
    tokenSymbol = json['tokenSymbol'];
    tokenAddress = json['tokenAddress'];
    chainSymbol = json['chainSymbol'];
  }
  String? chainId;
  String? netName;
  String? browserUrl;
  String? tokenSymbol;
  String? tokenAddress;
  String? chainSymbol;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['chainId'] = chainId;
    map['netName'] = netName;
    map['browserUrl'] = browserUrl;
    map['tokenSymbol'] = tokenSymbol;
    map['tokenAddress'] = tokenAddress;
    map['chainSymbol'] = chainSymbol;
    return map;
  }

}
