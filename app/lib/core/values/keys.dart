// ignore_for_file: constant_identifier_names

/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:43:49
 * @Description  : 常量对照表
 * @LastEditors  : Daen
 * @LastEditTime : 2022-04-21 11:04:38
 * @FilePath     : \flutter_metatel\lib\core\values\keys.dart
 */
const THEME = 'theme';
const NOT_FIRST_LOGIN = 'first-login';
const TOKEN = 'token';
const PRIVATE_KEY = 'privake_key';
const PHONE = 'phone';
const MYSELF_DISPLAY_NAME = 'myself_display_name';
const MYSELF_FIRST_NAME = 'myself_first_name';
const MYSELF_LAST_NAME = 'myself_last_name';
const MYSELF_AVATAR_INFO = 'myself_avatar_info';
const TURN_INFO = 'turn_info';
const AWS_INFO = 'aws_info';
const APP_VERSION = 'app_version';
const COMPLAINT_TIME = 'complaint_time';
const USER_AGREEMENT = 'user_agreement';
const CHANNEL_DELETE = 'channel_delete_1';
const USER_NAME_WITHOUT_DOMAIN = 'USER_NAME_WITHOUT_DOMAIN';

///联系人消息免打扰
const CUSTOM_USER_MESSAGE_SILENCE_ = "silence1_";

///消息通知声音
const ENABLE_MESSAGE_RING = "enable_message_ring";

///消息通知振动
const ENABLE_MESSAGE_VIBRATE = "enable_message_vibrate";
const OSS_INFO = 'oss_info';

const STS_INFO = 'sts_info';

const USER_MOBILE_MEX = 'user_mobile_mex';
const USER_NAME_MEX = 'user_name_mex';

const MYSELF_INFO_NEED_UPLOAD = 'myself_info_need_upload_1';
const MYSELF_INFO_NEED_UPLOAD_FIRST = 'myself_info_need_upload_first';

///自己的信息是否需要上传服务器
///
const KEY_BOARD_HEIGHT = 'key_board_height';

///键盘高度
const DEVICE_CODE = 'device_code';

///设备唯一标识 AndroidID or iderverfyfor...
const DOMAIN = 'domain';
const NODE = 'node';
const NODE_PORT = 'node_port';
const NODE_NEW = 'node_new';
const NODE_PORT_NEW = 'node_port_new';
const IOI_ID = 'ioi_id';
const SBT_ID = 'sbt_id';
const NODE_CONFIG = 'node_config';
const HAS_SET_PIN = 'has_set_pin';
const DESTRUCTION_PWD = 'metatel_pwd';
const WALLET_PWD = 'wallet_pwd';
const FILE_HELP_USER_NAME = 'file_help_user_name';
const HAS_ACTIVATE_WALLET = 'has_activate_wallet';
const IS_WALLET_OPEN = 'is_wallet_open';

///MsgDataSubjectEvent key
const MSG_DATA_SUBJECT_EVENT_AVATAR_MODEL = "msg_data_subject_event_avatar_model";

const SECURE_STORAGE_MNEMONIC = "secure_storage_mnemonic";
const SECURE_STORAGE_USER_ADDRESS_MEX = "secure_storage_user_address_mex";
const SECURE_STORAGE_PIN = "secure_storage_pin";

const WALLET_NETWORK = 'wallet_network';
const WALLET_SELECTED_NETWORK_CHANID = 'wallet_selected_network_chanid';
const WALLET_TOKEN_NETWORK = 'wallet_token_network';
const BIOMETRICS_OPEN = 'biometrics_open';
const ANNOUNCEMENT_KEY = 'announcement_key';

const EMOJI_TYPE = 'emoji_made'; //定制表情

///分享推广 旧
const NEED_POP_INVITE_DIALOG = 'need_pop_invite_dialog';
const IS_BIND_RECOMMENDER = 'is_bind_recommender';

///分享推广信息json包含分享推广 旧内容
const INVITE_INFO = 'invite_info';

///刷新token时间 针对oppo
const PUSH_TOKEN_TIME = "push_token_time_1";
const PROBE_TIME = 'probe_time';

const SUBMIT_MINING_INFO = 'submit_mining_info';
const SUBMIT_MINING_TIME = 'submit_mining_time';
const SUBMIT_BROWSER_CLICK = 'submit_browser_click';
const SUBMIT_BROWSER_CLICK_TIME = 'submit_browser_click_time';

const ICE_TRANSPORT_POLICY = 'iceTransportPolicy';

///官方号最新消息时间
const OFFICIAL_MSG_TIME_RECENT = 'official_msg_time_recent';
const RE_UPLOAD_AVARTAR = 're_upload_avatar';
const PROXY_IP = 'proxy_ip';
const PROXY_PORT = 'proxy_port';

///支付验证类型 0->密码 1->指纹,2->faceID
const PAY_IDENTIFY_TYPE = 'pay_identify_type';

///语言
const MULTI_LANGUAGE = 'multi_language';

const NEED_SUBMIT_SHARE_POSTER_NODE = 'need_submit_share_poster_node';

const SQLITE_INDEX = 'sqlite_index_5';

const EMOJI_UPDATE = 'emoji_update';
const SHOW_BROWSER = 'show_browser';
const LOCAL_AGENT = 'LOCAL_AGENT';
const ADS_INFO = 'ads_info';
const ADS_INFO_TIME = 'ads_info_time';
const LOGIN_FIRST_TIME = 'login_first_time';
const SQL_GROUP_INFO = 'sql_group_info_update';
const CURRENT_PROXY_ADDRESS = 'current_proxy_address';
const UNVAILABLE_PROXY_ADDRESS = 'unavailable_proxy_address';
const CHAT_MINING_VERIFICATION = 'chat_mining_verification';
const MINING_VALIDITY_PERIOD = 'mining_validity_period';
const AUTHENTICATION_INTERVAL = 'authentication_interval';
const AUTHENTICATION_IS_USED = 'authentication_used';
const SWITCH_NODE_INFO = 'switch_node_info';
const AMIN_WALLET = 'main_wallet';
const FIRST_UPDATE_WALLET = 'first_update_wallet';
const MESSAGE_TASK = 'message_task';
const AI_SESSION = 'ai_session';
const IS_SUBMIT_USER_INFO = 'is_submit_user_info';
const AUTHENTICATION_STATUS = 'authentication_status';
const AUTHENTICATION_TIMES = 'authentication_times';

/// 游戏公会
const GAME_CENTER_DATA = 'game_center_data';

/// 会员状态
const USER_STATUS = 'user_status';
