/*
Copyright (C) 2022-2022 ChatIO. All Rights Reserved.
*/


#ifndef CHATIO_FFI_H_
#define CHATIO_FFI_H_

/* Generated with cbindgen:0.24.5 */

/* Warning, this file is autogenerated by cbindgen. Don't modify this manually. */

#include <stdarg.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>

typedef struct ChatIOAuth ChatIOAuth;

typedef struct ChatIOFfiChatio ChatIOFfiChatio;

typedef ChatIOAuth *ChatIOAuthPtr;

typedef ChatIOFfiChatio *ChatIOFfiChatioPtr;

typedef bool (*ChatIOMessageCallBack)(const uint8_t *message,
                                      int32_t message_len,
                                      const char *from,
                                      const char *ID);

typedef bool (*ChatIOMessageErrorCallBack)(const char *from_name,
                                           uint32_t from_device_id,
                                           const char *ID);

typedef bool (*ChatIOUnauthorizedCallBack)(void);

typedef struct {
  unsigned char *body_ptr;
  uint32_t body_len;
  char *owner;
} ChatIOGroupItem;

typedef struct {
  ChatIOGroupItem master;
  ChatIOGroupItem *commits;
  uint32_t commits_len;
} ChatIOGroup;

typedef ChatIOGroup *ChatIOGroupPtr;

typedef struct {
  unsigned char *ptr;
  uint32_t len;
} ChatIOBytes;

typedef ChatIOBytes *ChatIOBytesPtr;

/**
 * # Safety
 *
 */
const char *FFI_AuthV1_GetToken(const char *auth_url,
                                const char *device_info,
                                const uint8_t *private_key,
                                const char *domain);

/**
 * # Safety
 *
 */
ChatIOAuthPtr FFI_AuthV2_Create(const char *auth_url,
                                const uint8_t *tmp_private_key,
                                const char *domain,
                                const char *phone,
                                const char *device_info);

/**
 * # Safety
 *
 */
const char *FFI_AuthV2_GetToken(ChatIOAuthPtr auth_ref, const char *msg_code);

/**
 * # Safety
 *
 */
int32_t FFI_AuthV2_PrivateKey(ChatIOAuthPtr auth_ref, uint8_t (*res_private_key)[32]);

/**
 * # Safety
 *
 */
void FFI_AuthV2_Delete(ChatIOAuthPtr auth_ref);

/**
 * # Safety
 *
 */
const char *FFI_AuthV2_QRCodeInfo(const char *auth_url,
                                  const char *uuid,
                                  const uint8_t *private_key);

/**
 * # Safety
 *
 */
int32_t FFI_AuthV2_QRCodeScan(const char *auth_url,
                              const char *uuid,
                              const char *qrcode_public_key_bs64,
                              const uint8_t *private_key,
                              const uint8_t *data_ptr,
                              uint32_t data_len);

/**
 * # Safety
 *
 * v3 auth 基于加密的协商认证
 */
const char *FFI_AuthV3_GetToken(const char *auth_url,
                                const char *extend_data,
                                const char *device_code,
                                const uint8_t *private_key,
                                uint8_t action);

/**
 * # Safety
 *
 * 设置代理
 */
void FFI_Chatio_Proxy(const char *proxy);

/**
 * # Safety
 *
 * 创建加密通道
 */
ChatIOFfiChatioPtr FFI_Chatio_New(const char *data_path,
                                  const char *server_url,
                                  const uint8_t *private_key,
                                  const char *domain,
                                  const char *token,
                                  ChatIOMessageCallBack msg_cb,
                                  ChatIOMessageErrorCallBack msg_err_cb,
                                  ChatIOUnauthorizedCallBack unauth_cb);

/**
 * # Safety
 *
 * 获取加密通道的设备名字
 */
const char *FFI_Chatio_GetDeviceName(ChatIOFfiChatioPtr ch_ref);

/**
 * # Safety
 *
 * 连接加密通道
 */
int32_t FFI_Chatio_Connect(ChatIOFfiChatioPtr ch_ref);

/**
 * # Safety
 *
 * 清除加密通道的会话
 */
int32_t FFI_Chatio_CleanSession(ChatIOFfiChatioPtr ch_ref, const char *name, uint32_t device_id);

/**
 * # Safety
 *
 * 使用加密通道发送消息
 */
const char *FFI_Chatio_SendMessage(ChatIOFfiChatioPtr ch_ref,
                                   uint16_t push_type,
                                   const char *const *to_list,
                                   uint32_t len,
                                   const uint8_t *message_ptr,
                                   uint32_t message_len);

/**
 * # Safety
 *
 * 接收加密通道的消息
 */
int32_t FFI_Chatio_RecvMessage(ChatIOFfiChatioPtr ch_ref);

/**
 * # Safety
 *
 * 释放加密通道
 */
void FFI_Chatio_Delete(ChatIOFfiChatioPtr ch_ref);

/**
 * # Safety
 *
 * This function
 */
int32_t FFI_Group_Create(const char *in_file,
                         const char *out_file,
                         const uint8_t *body_ptr,
                         uint32_t body_len,
                         const uint8_t *private_key_ptr);

/**
 * # Safety
 *
 * This function
 */
int32_t FFI_Group_Commit(const char *in_file,
                         const char *out_file,
                         const uint8_t *body,
                         uint32_t body_len,
                         const uint8_t *private_key);

/**
 * # Safety
 *
 * This function
 */
int32_t FFI_Group_Merge(const char *in_file, const char *merge_file, const char *out_file);

/**
 * # Safety
 *
 * This function
 */
ChatIOGroupPtr FFI_Group_Parse(const char *in_file);

/**
 * # Safety
 *
 * This function
 */
void FFI_Group_Free(ChatIOGroupPtr group);

void FFI_Util_Logger(int32_t max_level);

/**
 * # Safety
 *
 * 释放底层申请的const char的内存
 */
void FFI_Util_Free(char *ptr);

/**
 * # Safety
 *
 * 获取显示名字
 */
const char *FFI_Util_DisplayName(const uint8_t *private_key_ptr);

/**
 * # Safety
 *
 * 释放 Bytes 自定义字符串对象
 */
void FFI_Util_FreeBytes(ChatIOBytesPtr bytes);

/**
 * # Safety
 *
 * 指纹确认的显示内容
 */
const char *FFI_Util_DisplayableFingeprint(const char *remote_name, const uint8_t *private_key);

/**
 * # Safety
 *
 * 指纹确认的二维码内容
 */
const char *FFI_Util_ScannableFingeprint(const char *remote_name, const uint8_t *private_key);

/**
 * # Safety
 *
 * 扫描结果验证
 */
int32_t FFI_Util_ScannableFingeprintCompare(const char *remote_name,
                                            const char *combined,
                                            const uint8_t *private_key);

/**
 * # Safety
 *
 * 解密得到私钥
 */
int32_t FFI_Util_Private(const char *private_bs64,
                         const char *password,
                         uint8_t (*res_private_key)[32]);

/**
 * # Safety
 *
 * 生成私钥
 */
int32_t FFI_Util_CreatePrivateKey(uint8_t (*res_private_key)[32]);

/**
 * # Safety
 *
 * 生成密钥对
 */
int32_t FFI_Util_CreateKeyPair(uint8_t (*res_private_key)[32], uint8_t (*res_public_key)[32]);

/**
 * # Safety
 *
 * 文件加密
 */
const char *FFI_Util_FileEncrypt(const char *src, const char *dst);

/**
 * # Safety
 *
 * 文件解密
 */
int32_t FFI_Util_FileDecrypt(const char *src, const char *dst, const char *fragment);

/**
 * # Safety
 *
 * 文件解密到内存
 */
ChatIOBytesPtr FFI_Util_FileDecryptToMemory(const char *src, const char *fragment);

#endif /* CHATIO_FFI_H_ */
