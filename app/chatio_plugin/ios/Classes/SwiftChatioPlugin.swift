import Flutter
import UIKit


public class SwiftChatioPlugin: NSObject, FlutterPlugin {
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "chatio_plugin", binaryMessenger: registrar.messenger())
        let instance = SwiftChatioPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }

    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        result("iOS " + UIDevice.current.systemVersion)
    }

    public static func dummyMethodToEnforceBundling() {
        FFI_AuthV1_GetToken(nil,nil,nil,nil);
        FFI_AuthV3_GetToken(nil,nil,nil,nil,0);
        FFI_AuthV2_Create(nil,nil,nil,nil,nil);
        FFI_AuthV2_GetToken(nil,nil);
        FFI_AuthV2_PrivateKey(nil,nil);
        FFI_AuthV2_Delete(nil);
        FFI_AuthV2_QRCodeInfo(nil,nil,nil);
        FFI_AuthV2_QRCodeScan(nil, nil, nil, nil, nil, 0);

        FFI_Chatio_New(nil, nil, nil, nil, nil, nil, nil, nil);
        FFI_Chatio_GetDeviceName(nil);
        FFI_Chatio_Connect(nil);
        FFI_Chatio_CleanSession(nil,nil,0);
        FFI_Chatio_SendMessage(nil, 0, nil, 0, nil, 0);
        FFI_Chatio_RecvMessage(nil);
        FFI_Chatio_Delete(nil);
        FFI_Chatio_Proxy(nil);

        FFI_Group_Create(nil,nil,nil,0,nil);
        FFI_Group_Commit(nil, nil, nil, 0, nil);
        FFI_Group_Merge(nil, nil, nil);
        FFI_Group_Parse(nil);
        FFI_Group_Free(nil);


        FFI_Util_Free(nil);
        FFI_Util_Private(nil, nil, nil);
        FFI_Util_CreatePrivateKey(nil);
        FFI_Util_CreateKeyPair(nil, nil);
        FFI_Util_DisplayName(nil);
        FFI_Util_Logger(3);
        FFI_Util_FileEncrypt(nil, nil);
        FFI_Util_FileDecrypt(nil, nil, nil);
        FFI_Util_FileDecryptToMemory(nil, nil);
        FFI_Util_FreeBytes(nil);
        FFI_Util_DisplayableFingeprint(nil, nil);
        FFI_Util_ScannableFingeprint(nil, nil);
        FFI_Util_ScannableFingeprintCompare(nil, nil, nil);
    }
}
