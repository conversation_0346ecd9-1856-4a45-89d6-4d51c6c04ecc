#import "ChatioPlugin.h"
#if __has_include(<chatio_plugin/chatio_plugin-Swift.h>)
#import <chatio_plugin/chatio_plugin-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "chatio_plugin-Swift.h"
#endif

@implementation ChatioPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftChatioPlugin registerWithRegistrar:registrar];
}
@end
