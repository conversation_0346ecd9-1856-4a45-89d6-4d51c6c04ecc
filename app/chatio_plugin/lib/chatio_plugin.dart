/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-09-07 15:31:40
 * @Description  : iOS 依赖静态库需要
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-09-11 16:54:33
 * @FilePath     : /flutter_metatel/chatio_plugin/lib/chatio_plugin.dart
 */

import 'dart:async';

import 'package:flutter/services.dart';

class ChatioPlugin {
  static const MethodChannel _channel = MethodChannel('chatio_plugin');

  static Future<String?> get platformVersion async {
    final String? version = await _channel.invokeMethod('getPlatformVersion');
    return version;
  }
}
